package com.ms.bp.shared.common.io.delete;

import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.options.ImportOptions;

/**
 * 削除条件プロバイダー（データ駆動型削除ロジック）
 * 各データ行に基づいて動的に削除条件を生成する
 */
@FunctionalInterface
public interface DeleteConditionProvider<T extends DatabaseMappable> {
    
    /**
     * 単一データに基づいて削除条件を生成
     * @param dto 現在処理中のデータオブジェクト
     * @param options インポートオプション
     * @return 削除条件（nullの場合は削除をスキップ）
     */
    DeleteCondition generateDeleteCondition(T dto, ImportOptions options);
}
