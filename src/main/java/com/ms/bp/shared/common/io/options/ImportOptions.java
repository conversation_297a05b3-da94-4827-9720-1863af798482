package com.ms.bp.shared.common.io.options;

import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.delete.DeleteConditionProvider;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * インポートオプション（更新版）
 */
@Getter
@Builder
@Setter
public class ImportOptions {
    // 基本設定
    private final FileFormat format;
    private final boolean hasHeader;
    private final String delimiter;
    private final String targetTable;

    // バッチ処理設定
    @Builder.Default
    private final int batchSize = 1000;
    @Builder.Default
    private final boolean skipValidation = false;
    @Builder.Default
    private final boolean continueOnError = true;

    // UPSERT設定（複合主キー対応）
    private final List<String> keyColumns;

    @Builder.Default
    private final boolean upsertMode = false;
    // エラーファイル
    private final String errorFileName;
    // エラー処理設定
    @Builder.Default
    private final int maxFailedRecords = 100;  // デフォルトを100に設定

    // フィールドマッピング設定（CSV header -> Java字段名）
    private final Map<String, String> headerFieldMapping;

    // フィールドマッピングを有効にするかどうか
    @Builder.Default
    private final boolean enableFieldMapping = false;

    // 固定値フィールド設定（インポート時に追加される固定値）
    private final Map<String, Object> additionalFields;

    // 削除-挿入モード設定
    private final DeleteConditionProvider<?> deleteConditionProvider;

    /**
     * キー列のリストを取得（複合主キー対応）
     */
    public List<String> getKeyColumns() {
        return keyColumns != null ? keyColumns : Collections.emptyList();
    }

    /**
     * 削除-挿入モードかどうかを判定
     * @return 削除-挿入モードの場合true
     */
    public boolean isDeleteInsertMode() {
        return deleteConditionProvider != null;
    }

    /**
     * 型安全なDeleteConditionProviderの取得
     * @param <T> DTOの型
     * @return DeleteConditionProvider
     */
    @SuppressWarnings("unchecked")
    public <T> DeleteConditionProvider<T> getDeleteConditionProvider() {
        return (DeleteConditionProvider<T>) deleteConditionProvider;
    }

    /**
     * ビルダーの拡張（便利メソッド）
     */
    public static class ImportOptionsBuilder {
        /**
         * 複合キーを可変長引数で設定する便利メソッド
         */
        public ImportOptionsBuilder keyColumns(String... columns) {
            this.keyColumns = Arrays.asList(columns);
            return this;
        }

        /**
         * 複数の固定値フィールドを設定する便利メソッド
         */
        public ImportOptionsBuilder additionalFields(Map<String, Object> fields) {
            this.additionalFields = fields;
            return this;
        }

        /**
         * 削除-挿入モードを設定する便利メソッド
         * @param provider 削除条件プロバイダー
         * @return ビルダー
         */
        public ImportOptionsBuilder deleteInsertMode(DeleteConditionProvider<?> provider) {
            this.deleteConditionProvider = provider;
            return this;
        }
    }
}