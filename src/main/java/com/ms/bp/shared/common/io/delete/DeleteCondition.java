package com.ms.bp.shared.common.io.delete;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 削除条件を表現するクラス
 * WHERE句の列名と値のペアを管理する
 */
public class DeleteCondition {
    private final List<String> whereColumns;
    private final List<Object> whereValues;
    
    /**
     * コンストラクタ
     * @param whereColumns WHERE句の列名リスト
     * @param whereValues WHERE句の値リスト
     */
    public DeleteCondition(List<String> whereColumns, List<Object> whereValues) {
        if (whereColumns.size() != whereValues.size()) {
            throw new IllegalArgumentException("WHERE列数と値の数が一致しません");
        }
        this.whereColumns = new ArrayList<>(whereColumns);
        this.whereValues = new ArrayList<>(whereValues);
    }
    
    /**
     * 削除SQLを構築
     * @param tableName テーブル名
     * @return 削除SQL文
     */
    public String buildDeleteSql(String tableName) {
        if (whereColumns.isEmpty()) return null;
        
        String whereClause = whereColumns.stream()
            .map(col -> col + " = ?")
            .collect(Collectors.joining(" AND "));
            
        return String.format("DELETE FROM %s WHERE %s", tableName, whereClause);
    }
    
    /**
     * バッチ削除最適化用のキーを生成
     * 同じ列の組み合わせを持つ削除条件をグループ化するために使用
     * @return 最適化キー
     */
    public String getOptimizationKey() {
        return String.join(",", whereColumns);
    }
    
    /**
     * WHERE句の列名リストを取得
     * @return 列名リスト（コピー）
     */
    public List<String> getWhereColumns() { 
        return new ArrayList<>(whereColumns); 
    }
    
    /**
     * WHERE句の値リストを取得
     * @return 値リスト（コピー）
     */
    public List<Object> getWhereValues() { 
        return new ArrayList<>(whereValues); 
    }
}
