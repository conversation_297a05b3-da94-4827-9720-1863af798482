package com.ms.bp.shared.common.io.delete;

import java.util.ArrayList;
import java.util.List;

/**
 * 削除実行計画（バッチ最適化用）
 * 同じSQL文で実行される削除操作のパラメータを管理
 */
class DeleteExecutionPlan {
    private final List<Object> parameters;
    
    /**
     * コンストラクタ
     * @param parameters 削除SQLのパラメータリスト
     */
    public DeleteExecutionPlan(List<Object> parameters) {
        this.parameters = new ArrayList<>(parameters);
    }
    
    /**
     * パラメータリストを取得
     * @return パラメータリスト
     */
    public List<Object> getParameters() { 
        return parameters; 
    }
}
