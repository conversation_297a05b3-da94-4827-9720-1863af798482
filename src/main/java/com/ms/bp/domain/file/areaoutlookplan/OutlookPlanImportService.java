package com.ms.bp.domain.file.areaoutlookplan;

import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.model.OutlookPlanInfo;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.JinendoMasterRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.repository.impl.JinendoMasterRepositoryImpl;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ExportFileNameUtil;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.shared.util.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Supplier;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

public class OutlookPlanImportService extends AbstractImportService<OutlookPlanInfo> {

    private static final Logger logger = LoggerFactory.getLogger(OutlookPlanImportService.class);

    private final String inputFileType;

    public OutlookPlanImportService(String fileType)
    {
        logger.debug("OutlookPlanImportServiceが初期化されました");
        inputFileType = fileType;
    }
    @Override
    protected Class<OutlookPlanInfo> getDTOClass() {
        return OutlookPlanInfo.class;
    }

    @Override
    protected ImportOptions buildImportOptions()
    {
        // CSVヘッダーフィールドマッピングを構築
        Map<String, String> headerMapping = new HashMap<>();

        headerMapping.put("No", "no");
        headerMapping.put("移管元部署", "ikanmotoBusho");
        headerMapping.put("移管先部署", "ikansakiBusho");
        headerMapping.put("ｴﾘｱｺｰﾄﾞ", "areaCode");
        headerMapping.put("ｴﾘｱ名漢字", "areaName");
        headerMapping.put("ｸﾞﾙｰﾌﾟCD", "groupCode");
        headerMapping.put("ﾕﾆｯﾄCD", "unitCode");
        headerMapping.put("担当名", "tantoshaName");
        headerMapping.put("採算CD7桁", "saisanCode");
        headerMapping.put("採算名", "saisanName");
        headerMapping.put("企業CD", "kigyoCode");
        headerMapping.put("企業名", "kigyoName");
        headerMapping.put("ｶﾃｺﾞﾘ", "kategori");
        headerMapping.put("ｻﾌﾞｶﾃ", "subKategori");
        headerMapping.put("業態(事業計画)", "gyotai");
        headerMapping.put("変更後取組区分", "afterTorikumKbn");
        headerMapping.put("業態比率", "gyotaiHiritsu");

        // 各種金額
        headerMapping.put("4月(計画)_総売上高(在庫)", "planAprAllSalInven");
        headerMapping.put("4月(計画)_総売上高(直送)", "planAprAllSalChokusou");
        headerMapping.put("4月(計画)_返品(在庫)", "planAprReturnInven");
        headerMapping.put("4月(計画)_返品(直送)", "planAprReturnChokusou");
        headerMapping.put("4月(計画)_リベート(在庫)", "planAprRebateInven");
        headerMapping.put("4月(計画)_リベート(直送)", "planAprRebateChokusou");
        headerMapping.put("4月(計画)_センターフィ(在庫)", "planAprCenterFeeInven");
        headerMapping.put("4月(計画)_センターフィ(直送)", "planAprCenterFeeChokusou");
        headerMapping.put("4月(計画)_直接利益(在庫)", "planAprChokuRiekiInven");
        headerMapping.put("4月(計画)_直接利益(直送)", "planAprChokuRiekiChokusou");
        headerMapping.put("5月(計画)_総売上高(在庫)", "planMayAllSalInven");
        headerMapping.put("5月(計画)_総売上高(直送)", "planMayAllSalChokusou");
        headerMapping.put("5月(計画)_返品(在庫)", "planMayReturnInven");
        headerMapping.put("5月(計画)_返品(直送)", "planMayReturnChokusou");
        headerMapping.put("5月(計画)_リベート(在庫)", "planMayRebateInven");
        headerMapping.put("5月(計画)_リベート(直送)", "planMayRebateChokusou");
        headerMapping.put("5月(計画)_センターフィ(在庫)", "planMayCenterFeeInven");
        headerMapping.put("5月(計画)_センターフィ(直送)", "planMayCenterFeeChokusou");
        headerMapping.put("5月(計画)_直接利益(在庫)", "planMayChokuRiekiInven");
        headerMapping.put("5月(計画)_直接利益(直送)", "planMayChokuRiekiChokusou");
        headerMapping.put("6月(計画)_総売上高(在庫)", "planJunAllSalInven");
        headerMapping.put("6月(計画)_総売上高(直送)", "planJunAllSalChokusou");
        headerMapping.put("6月(計画)_返品(在庫)", "planJunReturnInven");
        headerMapping.put("6月(計画)_返品(直送)", "planJunReturnChokusou");
        headerMapping.put("6月(計画)_リベート(在庫)", "planJunRebateInven");
        headerMapping.put("6月(計画)_リベート(直送)", "planJunRebateChokusou");
        headerMapping.put("6月(計画)_センターフィ(在庫)", "planJunCenterFeeInven");
        headerMapping.put("6月(計画)_センターフィ(直送)", "planJunCenterFeeChokusou");
        headerMapping.put("6月(計画)_直接利益(在庫)", "planJunChokuRiekiInven");
        headerMapping.put("6月(計画)_直接利益(直送)", "planJunChokuRiekiChokusou");
        headerMapping.put("7月(計画)_総売上高(在庫)", "planJulAllSalInven");
        headerMapping.put("7月(計画)_総売上高(直送)", "planJulAllSalChokusou");
        headerMapping.put("7月(計画)_返品(在庫)", "planJulReturnInven");
        headerMapping.put("7月(計画)_返品(直送)", "planJulReturnChokusou");
        headerMapping.put("7月(計画)_リベート(在庫)", "planJulRebateInven");
        headerMapping.put("7月(計画)_リベート(直送)", "planJulRebateChokusou");
        headerMapping.put("7月(計画)_センターフィ(在庫)", "planJulCenterFeeInven");
        headerMapping.put("7月(計画)_センターフィ(直送)", "planJulCenterFeeChokusou");
        headerMapping.put("7月(計画)_直接利益(在庫)", "planJulChokuRiekiInven");
        headerMapping.put("7月(計画)_直接利益(直送)", "planJulChokuRiekiChokusou");
        headerMapping.put("8月(計画)_総売上高(在庫)", "planAugAllSalInven");
        headerMapping.put("8月(計画)_総売上高(直送)", "planAugAllSalChokusou");
        headerMapping.put("8月(計画)_返品(在庫)", "planAugReturnInven");
        headerMapping.put("8月(計画)_返品(直送)", "planAugReturnChokusou");
        headerMapping.put("8月(計画)_リベート(在庫)", "planAugRebateInven");
        headerMapping.put("8月(計画)_リベート(直送)", "planAugRebateChokusou");
        headerMapping.put("8月(計画)_センターフィ(在庫)", "planAugCenterFeeInven");
        headerMapping.put("8月(計画)_センターフィ(直送)", "planAugCenterFeeChokusou");
        headerMapping.put("8月(計画)_直接利益(在庫)", "planAugChokuRiekiInven");
        headerMapping.put("8月(計画)_直接利益(直送)", "planAugChokuRiekiChokusou");
        headerMapping.put("9月(計画)_総売上高(在庫)", "planSepAllSalInven");
        headerMapping.put("9月(計画)_総売上高(直送)", "planSepAllSalChokusou");
        headerMapping.put("9月(計画)_返品(在庫)", "planSepReturnInven");
        headerMapping.put("9月(計画)_返品(直送)", "planSepReturnChokusou");
        headerMapping.put("9月(計画)_リベート(在庫)", "planSepRebateInven");
        headerMapping.put("9月(計画)_リベート(直送)", "planSepRebateChokusou");
        headerMapping.put("9月(計画)_センターフィ(在庫)", "planSepCenterFeeInven");
        headerMapping.put("9月(計画)_センターフィ(直送)", "planSepCenterFeeChokusou");
        headerMapping.put("9月(計画)_直接利益(在庫)", "planSepChokuRiekiInven");
        headerMapping.put("9月(計画)_直接利益(直送)", "planSepChokuRiekiChokusou");
        headerMapping.put("10月(計画)_総売上高(在庫)", "planOctAllSalInven");
        headerMapping.put("10月(計画)_総売上高(直送)", "planOctAllSalChokusou");
        headerMapping.put("10月(計画)_返品(在庫)", "planOctReturnInven");
        headerMapping.put("10月(計画)_返品(直送)", "planOctReturnChokusou");
        headerMapping.put("10月(計画)_リベート(在庫)", "planOctRebateInven");
        headerMapping.put("10月(計画)_リベート(直送)", "planOctRebateChokusou");
        headerMapping.put("10月(計画)_センターフィ(在庫)", "planOctCenterFeeInven");
        headerMapping.put("10月(計画)_センターフィ(直送)", "planOctCenterFeeChokusou");
        headerMapping.put("10月(計画)_直接利益(在庫)", "planOctChokuRiekiInven");
        headerMapping.put("10月(計画)_直接利益(直送)", "planOctChokuRiekiChokusou");
        headerMapping.put("11月(計画)_総売上高(在庫)", "planNovAllSalInven");
        headerMapping.put("11月(計画)_総売上高(直送)", "planNovAllSalChokusou");
        headerMapping.put("11月(計画)_返品(在庫)", "planNovReturnInven");
        headerMapping.put("11月(計画)_返品(直送)", "planNovReturnChokusou");
        headerMapping.put("11月(計画)_リベート(在庫)", "planNovRebateInven");
        headerMapping.put("11月(計画)_リベート(直送)", "planNovRebateChokusou");
        headerMapping.put("11月(計画)_センターフィ(在庫)", "planNovCenterFeeInven");
        headerMapping.put("11月(計画)_センターフィ(直送)", "planNovCenterFeeChokusou");
        headerMapping.put("11月(計画)_直接利益(在庫)", "planNovChokuRiekiInven");
        headerMapping.put("11月(計画)_直接利益(直送)", "planNovChokuRiekiChokusou");
        headerMapping.put("12月(計画)_総売上高(在庫)", "planDecAllSalInven");
        headerMapping.put("12月(計画)_総売上高(直送)", "planDecAllSalChokusou");
        headerMapping.put("12月(計画)_返品(在庫)", "planDecReturnInven");
        headerMapping.put("12月(計画)_返品(直送)", "planDecReturnChokusou");
        headerMapping.put("12月(計画)_リベート(在庫)", "planDecRebateInven");
        headerMapping.put("12月(計画)_リベート(直送)", "planDecRebateChokusou");
        headerMapping.put("12月(計画)_センターフィ(在庫)", "planDecCenterFeeInven");
        headerMapping.put("12月(計画)_センターフィ(直送)", "planDecCenterFeeChokusou");
        headerMapping.put("12月(計画)_直接利益(在庫)", "planDecChokuRiekiInven");
        headerMapping.put("12月(計画)_直接利益(直送)", "planDecChokuRiekiChokusou");
        headerMapping.put("1月(計画)_総売上高(在庫)", "planJanAllSalInven");
        headerMapping.put("1月(計画)_総売上高(直送)", "planJanAllSalChokusou");
        headerMapping.put("1月(計画)_返品(在庫)", "planJanReturnInven");
        headerMapping.put("1月(計画)_返品(直送)", "planJanReturnChokusou");
        headerMapping.put("1月(計画)_リベート(在庫)", "planJanRebateInven");
        headerMapping.put("1月(計画)_リベート(直送)", "planJanRebateChokusou");
        headerMapping.put("1月(計画)_センターフィ(在庫)", "planJanCenterFeeInven");
        headerMapping.put("1月(計画)_センターフィ(直送)", "planJanCenterFeeChokusou");
        headerMapping.put("1月(計画)_直接利益(在庫)", "planJanChokuRiekiInven");
        headerMapping.put("1月(計画)_直接利益(直送)", "planJanChokuRiekiChokusou");
        headerMapping.put("2月(計画)_総売上高(在庫)", "planFebAllSalInven");
        headerMapping.put("2月(計画)_総売上高(直送)", "planFebAllSalChokusou");
        headerMapping.put("2月(計画)_返品(在庫)", "planFebReturnInven");
        headerMapping.put("2月(計画)_返品(直送)", "planFebReturnChokusou");
        headerMapping.put("2月(計画)_リベート(在庫)", "planFebRebateInven");
        headerMapping.put("2月(計画)_リベート(直送)", "planFebRebateChokusou");
        headerMapping.put("2月(計画)_センターフィ(在庫)", "planFebCenterFeeInven");
        headerMapping.put("2月(計画)_センターフィ(直送)", "planFebCenterFeeChokusou");
        headerMapping.put("2月(計画)_直接利益(在庫)", "planFebChokuRiekiInven");
        headerMapping.put("2月(計画)_直接利益(直送)", "planFebChokuRiekiChokusou");
        headerMapping.put("3月(計画)_総売上高(在庫)", "planMarAllSalInven");
        headerMapping.put("3月(計画)_総売上高(直送)", "planMarAllSalChokusou");
        headerMapping.put("3月(計画)_返品(在庫)", "planMarReturnInven");
        headerMapping.put("3月(計画)_返品(直送)", "planMarReturnChokusou");
        headerMapping.put("3月(計画)_リベート(在庫)", "planMarRebateInven");
        headerMapping.put("3月(計画)_リベート(直送)", "planMarRebateChokusou");
        headerMapping.put("3月(計画)_センターフィ(在庫)", "planMarCenterFeeInven");
        headerMapping.put("3月(計画)_センターフィ(直送)", "planMarCenterFeeChokusou");
        headerMapping.put("3月(計画)_直接利益(在庫)", "planMarChokuRiekiInven");
        headerMapping.put("3月(計画)_直接利益(直送)", "planMarChokuRiekiChokusou");

        headerMapping.put("4月(実績見通し)_総売上高(在庫)", "outlookAprAllSalInven");
        headerMapping.put("4月(実績見通し)_総売上高(直送)", "outlookAprAllSalChokusou");
        headerMapping.put("4月(実績見通し)_返品(在庫)", "outlookAprReturnInven");
        headerMapping.put("4月(実績見通し)_返品(直送)", "outlookAprReturnChokusou");
        headerMapping.put("4月(実績見通し)_リベート(在庫)", "outlookAprRebateInven");
        headerMapping.put("4月(実績見通し)_リベート(直送)", "outlookAprRebateChokusou");
        headerMapping.put("4月(実績見通し)_センターフィ(在庫)", "outlookAprCenterFeeInven");
        headerMapping.put("4月(実績見通し)_センターフィ(直送)", "outlookAprCenterFeeChokusou");
        headerMapping.put("4月(実績見通し)_直接利益(在庫)", "outlookAprChokuRiekiInven");
        headerMapping.put("4月(実績見通し)_直接利益(直送)", "outlookAprChokuRiekiChokusou");
        headerMapping.put("5月(実績見通し)_総売上高(在庫)", "outlookMayAllSalInven");
        headerMapping.put("5月(実績見通し)_総売上高(直送)", "outlookMayAllSalChokusou");
        headerMapping.put("5月(実績見通し)_返品(在庫)", "outlookMayReturnInven");
        headerMapping.put("5月(実績見通し)_返品(直送)", "outlookMayReturnChokusou");
        headerMapping.put("5月(実績見通し)_リベート(在庫)", "outlookMayRebateInven");
        headerMapping.put("5月(実績見通し)_リベート(直送)", "outlookMayRebateChokusou");
        headerMapping.put("5月(実績見通し)_センターフィ(在庫)", "outlookMayCenterFeeInven");
        headerMapping.put("5月(実績見通し)_センターフィ(直送)", "outlookMayCenterFeeChokusou");
        headerMapping.put("5月(実績見通し)_直接利益(在庫)", "outlookMayChokuRiekiInven");
        headerMapping.put("5月(実績見通し)_直接利益(直送)", "outlookMayChokuRiekiChokusou");
        headerMapping.put("6月(実績見通し)_総売上高(在庫)", "outlookJunAllSalInven");
        headerMapping.put("6月(実績見通し)_総売上高(直送)", "outlookJunAllSalChokusou");
        headerMapping.put("6月(実績見通し)_返品(在庫)", "outlookJunReturnInven");
        headerMapping.put("6月(実績見通し)_返品(直送)", "outlookJunReturnChokusou");
        headerMapping.put("6月(実績見通し)_リベート(在庫)", "outlookJunRebateInven");
        headerMapping.put("6月(実績見通し)_リベート(直送)", "outlookJunRebateChokusou");
        headerMapping.put("6月(実績見通し)_センターフィ(在庫)", "outlookJunCenterFeeInven");
        headerMapping.put("6月(実績見通し)_センターフィ(直送)", "outlookJunCenterFeeChokusou");
        headerMapping.put("6月(実績見通し)_直接利益(在庫)", "outlookJunChokuRiekiInven");
        headerMapping.put("6月(実績見通し)_直接利益(直送)", "outlookJunChokuRiekiChokusou");
        headerMapping.put("7月(実績見通し)_総売上高(在庫)", "outlookJulAllSalInven");
        headerMapping.put("7月(実績見通し)_総売上高(直送)", "outlookJulAllSalChokusou");
        headerMapping.put("7月(実績見通し)_返品(在庫)", "outlookJulReturnInven");
        headerMapping.put("7月(実績見通し)_返品(直送)", "outlookJulReturnChokusou");
        headerMapping.put("7月(実績見通し)_リベート(在庫)", "outlookJulRebateInven");
        headerMapping.put("7月(実績見通し)_リベート(直送)", "outlookJulRebateChokusou");
        headerMapping.put("7月(実績見通し)_センターフィ(在庫)", "outlookJulCenterFeeInven");
        headerMapping.put("7月(実績見通し)_センターフィ(直送)", "outlookJulCenterFeeChokusou");
        headerMapping.put("7月(実績見通し)_直接利益(在庫)", "outlookJulChokuRiekiInven");
        headerMapping.put("7月(実績見通し)_直接利益(直送)", "outlookJulChokuRiekiChokusou");
        headerMapping.put("8月(実績見通し)_総売上高(在庫)", "outlookAugAllSalInven");
        headerMapping.put("8月(実績見通し)_総売上高(直送)", "outlookAugAllSalChokusou");
        headerMapping.put("8月(実績見通し)_返品(在庫)", "outlookAugReturnInven");
        headerMapping.put("8月(実績見通し)_返品(直送)", "outlookAugReturnChokusou");
        headerMapping.put("8月(実績見通し)_リベート(在庫)", "outlookAugRebateInven");
        headerMapping.put("8月(実績見通し)_リベート(直送)", "outlookAugRebateChokusou");
        headerMapping.put("8月(実績見通し)_センターフィ(在庫)", "outlookAugCenterFeeInven");
        headerMapping.put("8月(実績見通し)_センターフィ(直送)", "outlookAugCenterFeeChokusou");
        headerMapping.put("8月(実績見通し)_直接利益(在庫)", "outlookAugChokuRiekiInven");
        headerMapping.put("8月(実績見通し)_直接利益(直送)", "outlookAugChokuRiekiChokusou");
        headerMapping.put("9月(実績見通し)_総売上高(在庫)", "outlookSepAllSalInven");
        headerMapping.put("9月(実績見通し)_総売上高(直送)", "outlookSepAllSalChokusou");
        headerMapping.put("9月(実績見通し)_返品(在庫)", "outlookSepReturnInven");
        headerMapping.put("9月(実績見通し)_返品(直送)", "outlookSepReturnChokusou");
        headerMapping.put("9月(実績見通し)_リベート(在庫)", "outlookSepRebateInven");
        headerMapping.put("9月(実績見通し)_リベート(直送)", "outlookSepRebateChokusou");
        headerMapping.put("9月(実績見通し)_センターフィ(在庫)", "outlookSepCenterFeeInven");
        headerMapping.put("9月(実績見通し)_センターフィ(直送)", "outlookSepCenterFeeChokusou");
        headerMapping.put("9月(実績見通し)_直接利益(在庫)", "outlookSepChokuRiekiInven");
        headerMapping.put("9月(実績見通し)_直接利益(直送)", "outlookSepChokuRiekiChokusou");
        headerMapping.put("10月(実績見通し)_総売上高(在庫)", "outlookOctAllSalInven");
        headerMapping.put("10月(実績見通し)_総売上高(直送)", "outlookOctAllSalChokusou");
        headerMapping.put("10月(実績見通し)_返品(在庫)", "outlookOctReturnInven");
        headerMapping.put("10月(実績見通し)_返品(直送)", "outlookOctReturnChokusou");
        headerMapping.put("10月(実績見通し)_リベート(在庫)", "outlookOctRebateInven");
        headerMapping.put("10月(実績見通し)_リベート(直送)", "outlookOctRebateChokusou");
        headerMapping.put("10月(実績見通し)_センターフィ(在庫)", "outlookOctCenterFeeInven");
        headerMapping.put("10月(実績見通し)_センターフィ(直送)", "outlookOctCenterFeeChokusou");
        headerMapping.put("10月(実績見通し)_直接利益(在庫)", "outlookOctChokuRiekiInven");
        headerMapping.put("10月(実績見通し)_直接利益(直送)", "outlookOctChokuRiekiChokusou");
        headerMapping.put("11月(実績見通し)_総売上高(在庫)", "outlookNovAllSalInven");
        headerMapping.put("11月(実績見通し)_総売上高(直送)", "outlookNovAllSalChokusou");
        headerMapping.put("11月(実績見通し)_返品(在庫)", "outlookNovReturnInven");
        headerMapping.put("11月(実績見通し)_返品(直送)", "outlookNovReturnChokusou");
        headerMapping.put("11月(実績見通し)_リベート(在庫)", "outlookNovRebateInven");
        headerMapping.put("11月(実績見通し)_リベート(直送)", "outlookNovRebateChokusou");
        headerMapping.put("11月(実績見通し)_センターフィ(在庫)", "outlookNovCenterFeeInven");
        headerMapping.put("11月(実績見通し)_センターフィ(直送)", "outlookNovCenterFeeChokusou");
        headerMapping.put("11月(実績見通し)_直接利益(在庫)", "outlookNovChokuRiekiInven");
        headerMapping.put("11月(実績見通し)_直接利益(直送)", "outlookNovChokuRiekiChokusou");
        headerMapping.put("12月(実績見通し)_総売上高(在庫)", "outlookDecAllSalInven");
        headerMapping.put("12月(実績見通し)_総売上高(直送)", "outlookDecAllSalChokusou");
        headerMapping.put("12月(実績見通し)_返品(在庫)", "outlookDecReturnInven");
        headerMapping.put("12月(実績見通し)_返品(直送)", "outlookDecReturnChokusou");
        headerMapping.put("12月(実績見通し)_リベート(在庫)", "outlookDecRebateInven");
        headerMapping.put("12月(実績見通し)_リベート(直送)", "outlookDecRebateChokusou");
        headerMapping.put("12月(実績見通し)_センターフィ(在庫)", "outlookDecCenterFeeInven");
        headerMapping.put("12月(実績見通し)_センターフィ(直送)", "outlookDecCenterFeeChokusou");
        headerMapping.put("12月(実績見通し)_直接利益(在庫)", "outlookDecChokuRiekiInven");
        headerMapping.put("12月(実績見通し)_直接利益(直送)", "outlookDecChokuRiekiChokusou");
        headerMapping.put("1月(実績見通し)_総売上高(在庫)", "outlookJanAllSalInven");
        headerMapping.put("1月(実績見通し)_総売上高(直送)", "outlookJanAllSalChokusou");
        headerMapping.put("1月(実績見通し)_返品(在庫)", "outlookJanReturnInven");
        headerMapping.put("1月(実績見通し)_返品(直送)", "outlookJanReturnChokusou");
        headerMapping.put("1月(実績見通し)_リベート(在庫)", "outlookJanRebateInven");
        headerMapping.put("1月(実績見通し)_リベート(直送)", "outlookJanRebateChokusou");
        headerMapping.put("1月(実績見通し)_センターフィ(在庫)", "outlookJanCenterFeeInven");
        headerMapping.put("1月(実績見通し)_センターフィ(直送)", "outlookJanCenterFeeChokusou");
        headerMapping.put("1月(実績見通し)_直接利益(在庫)", "outlookJanChokuRiekiInven");
        headerMapping.put("1月(実績見通し)_直接利益(直送)", "outlookJanChokuRiekiChokusou");
        headerMapping.put("2月(実績見通し)_総売上高(在庫)", "outlookFebAllSalInven");
        headerMapping.put("2月(実績見通し)_総売上高(直送)", "outlookFebAllSalChokusou");
        headerMapping.put("2月(実績見通し)_返品(在庫)", "outlookFebReturnInven");
        headerMapping.put("2月(実績見通し)_返品(直送)", "outlookFebReturnChokusou");
        headerMapping.put("2月(実績見通し)_リベート(在庫)", "outlookFebRebateInven");
        headerMapping.put("2月(実績見通し)_リベート(直送)", "outlookFebRebateChokusou");
        headerMapping.put("2月(実績見通し)_センターフィ(在庫)", "outlookFebCenterFeeInven");
        headerMapping.put("2月(実績見通し)_センターフィ(直送)", "outlookFebCenterFeeChokusou");
        headerMapping.put("2月(実績見通し)_直接利益(在庫)", "outlookFebChokuRiekiInven");
        headerMapping.put("2月(実績見通し)_直接利益(直送)", "outlookFebChokuRiekiChokusou");
        headerMapping.put("3月(実績見通し)_総売上高(在庫)", "outlookMarAllSalInven");
        headerMapping.put("3月(実績見通し)_総売上高(直送)", "outlookMarAllSalChokusou");
        headerMapping.put("3月(実績見通し)_返品(在庫)", "outlookMarReturnInven");
        headerMapping.put("3月(実績見通し)_返品(直送)", "outlookMarReturnChokusou");
        headerMapping.put("3月(実績見通し)_リベート(在庫)", "outlookMarRebateInven");
        headerMapping.put("3月(実績見通し)_リベート(直送)", "outlookMarRebateChokusou");
        headerMapping.put("3月(実績見通し)_センターフィ(在庫)", "outlookMarCenterFeeInven");
        headerMapping.put("3月(実績見通し)_センターフィ(直送)", "outlookMarCenterFeeChokusou");
        headerMapping.put("3月(実績見通し)_直接利益(在庫)", "outlookMarChokuRiekiInven");
        headerMapping.put("3月(実績見通し)_直接利益(直送)", "outlookMarChokuRiekiChokusou");

        headerMapping.put("ファイル情報1", "fileInfo1");
        headerMapping.put("ファイル情報2", "fileInfo2");

        // 固定値フィールドを構築（RequestContextからユーザー情報を取得）
        Map<String, Object> additionalFields = buildAdditionalFields();

        String tblName = "T_HNSH_MTSH_KKK_SSNKN_TN_C";
        if(inputFileType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            tblName = "T_AREA_MTSH_KKK_SSNKN_TN_C";
        }

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(BusinessConstants.CSV_UPLOAD_BATCH_SIZE)
                .targetTable(tblName)
                // 複合主キー：年度 + グループコード + 採算管理単位コード
                .keyColumns("NENDO", "GROUP_CODE", "SSNKN_TNCD")
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName(ExportFileNameUtil.getFileNameByFileType(inputFileType))
                .enableFieldMapping(true)  // フィールドマッピングを有効化
                .headerFieldMapping(headerMapping)
                .additionalFields(additionalFields)
                .build();
    }

    @Override
    protected String getDataType() {
        // 見通し・計画_採算管理単位C別＜本社＞
        // 見通し・計画_採算管理単位C別＜エリア＞
        return inputFileType;
    }

    @Override
    protected DataValidator getDataValidator() {
        return new DTODataValidator<>(OutlookPlanInfo.class, this::validateCustomLogic);
    }

    /**
     * 固定値フィールドを構築（RequestContextからユーザー情報を取得）
     */
    private Map<String, Object> buildAdditionalFields() {
        Map<String, Object> additionalFields = new HashMap<>();

        // RequestContextからユーザー情報を取得
        UserInfo userInfo = RequestContext.getUserInfo();

        additionalFields.put("NENDO", DateUtil.getNextFiscalYear());
        if (userInfo != null) {
            // ユーザー情報が取得できた場合、実際の値を設定
            String companyCode = userInfo.getSystemOperationCompanyCode();
            String shainCode = userInfo.getShainCode();

            additionalFields.put("TRK_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("TRK_SHAIN_CODE", shainCode);
            additionalFields.put("KSHN_SYSTM_UNYO_KIGYO_CODE", companyCode);
            additionalFields.put("KSHN_SHAIN_CODE", shainCode);

            logger.debug("ユーザー情報から固定値フィールドを設定: 企業コード={}, 社員コード={}",
                    companyCode, shainCode);
        }

        return additionalFields;
    }

    /**
     * カスタムビジネスロジック検証
     * アノテーション検証では対応できない複雑な検証ロジックを実装
     *
     * 検証内容：
     * 1. 権限チェック（エリアコード、移管先エリアコード）
     * 2. データベース存在性チェック（移管先エリア、グループ、ユニット）
     * 3. 業務ルールに基づく検証
     *
     * @param data 検証対象のデータ
     * @param options インポートオプション
     * @return 検証エラーのリスト
     */
    protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
        List<ValidationError> errors = new ArrayList<>();

        // RequestContextからユーザー情報を取得して権限チェック
        UserInfo userInfo = RequestContext.getUserInfo();
        if (userInfo != null) {
            validatePermissions(data, userInfo, errors);
        }

        return errors;
    }

    /**
     * 権限チェック処理
     */
    private void validatePermissions(Map<String, Object> data, UserInfo userInfo, List<ValidationError> errors) {
        logger.debug("権限チェック開始: ユーザー={}", userInfo.getShainCode());

        // エリアコードの権限チェック
        String areaCode = (String) data.get("areaCode");
        if (areaCode != null && !areaCode.trim().isEmpty()) {
            validateAreaPermission(areaCode, "areaCode", userInfo, errors);
        }

        // エリア、採算管理単位コードの権限チェック
        String saisanCode = (String) data.get("saisanCode");
        String groupCode = (String) data.get("groupCode");
        String unitCode = (String) data.get("unitCode");
        if (saisanCode != null && !saisanCode.trim().isEmpty()) {
            validateSaisanPermission(areaCode, saisanCode, groupCode, unitCode,"saisanCode", userInfo, errors);
        }

        logger.debug("権限チェック完了: エラー数={}", errors.size());
    }

    /**
     * エリア権限検証
     * ユーザーのエリア権限に基づいてアクセス可能なエリアコードかどうかを検証する
     * 検証ロジック：
     * 1. userInfo.areaInfosが設定されている場合：areaInfos内のareaCodeと照合
     * 2. userInfo.areaInfosが空の場合：userInfo.areaCodeと照合
     * 3. いずれにも該当しない場合は権限エラー
     */
    private void validateAreaPermission(String areaCode, String fieldName, UserInfo userInfo, List<ValidationError> errors) {
        try {
            logger.trace("エリア権限チェック開始: areaCode={}, fieldName={}, user={}",
                    areaCode, fieldName, userInfo.getShainCode());

            boolean hasPermission;
            List<AreaInfo> areaInfos = userInfo.getAreaInfos();

            if (areaInfos != null && !areaInfos.isEmpty()) {
                // areaInfosが設定されている場合：コレクション内のareaCodeと照合
                hasPermission = areaInfos.stream()
                        .anyMatch(areaInfo -> areaCode.equals(areaInfo.getAreaCode()));
            } else {
                // areaInfosが空またはnullの場合：userInfo.areaCodeと照合
                hasPermission = areaCode.equals(userInfo.getAreaCode());
            }

            if (!hasPermission) {
                errors.add(new ValidationError(fieldName, areaCode, formatMessage(GlobalMessageConstants.ERR_028)));
            }

        } catch (Exception e) {
            logger.error("権限チェック中にエラーが発生しました: areaCode={}, user={}",
                    areaCode, userInfo.getShainCode(), e);
            errors.add(new ValidationError(fieldName, areaCode, formatMessage(GlobalMessageConstants.ERR_028)));
        }
    }

    /**
     * エリア、採算管理単位コードの権限チェック
     */
    private void validateSaisanPermission(String areaCode,
                                          String saisanCode,
                                          String groupCode,
                                          String unitCode,
                                          String fieldName,
                                          UserInfo userInfo,
                                          List<ValidationError> errors) {

        try {
            boolean hasPermission = true;

            // レスポンス取得
            // a.CSV.ｴﾘｱｺｰﾄﾞのチェック
            // パラメータ.エリアコード = CSV.ｴﾘｱｺｰﾄﾞ　※1エリアずつのアップロードのため。
            List<AreaInfo> areaInfos = userInfo.getAreaInfos();

            if (areaInfos != null && !areaInfos.isEmpty()) {
                // areaInfosが設定されている場合：コレクション内のareaCodeと照合
                hasPermission = areaInfos.stream()
                        .anyMatch(areaInfo -> areaCode.equals(areaInfo.getAreaCode()));
            } else {
                // areaInfosが空またはnullの場合：userInfo.areaCodeと照合
                hasPermission = areaCode.equals(userInfo.getAreaCode());
            }

            // b.CSV.ｸﾞﾙｰﾌﾟCDのチェック
            // レスポンス.グループコード = CSV.ｸﾞﾙｰﾌﾟCD　※自グループにしか権限がないため。
            if(hasPermission)
            {
                hasPermission = groupCode.equals(userInfo.getGroupCode());
            }
            // レスポンス.役職区分判定要否 = 1:要　の場合　AND
            // レスポンス.役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
            if(userInfo.getPositionSpecialCheck() == BusinessConstants.REQUIRE_POSITION_SPECIAL_CHECK &&
                (userInfo.getPositionCode() == BusinessConstants.POSITION_CODE_51 ||
                        userInfo.getPositionCode() == BusinessConstants.POSITION_CODE_61))
            {

                // c.CSV.ﾕﾆｯﾄCDのチェック
                // レスポンス.ユニットコード = CSV.ﾕﾆｯﾄCD　※自ユニットにしか権限がないため。
                if(hasPermission)
                {
                    hasPermission = unitCode.equals(userInfo.getUnitCode());
                }

                // d.CSV.採算CD7桁のチェック
                // CSV.ｴﾘｱｺｰﾄﾞ = （次年度計画マスタ.適用エリアコード　AND　次年度計画マスタ.年度 = {今年度 ※年度の取得は設計書_共通.xlsxを参照}）AND
                // CSV.ｸﾞﾙｰﾌﾟCD = 次年度計画マスタ.適用グループコード（適用エリアコードと同レコードのデータ）AND
                // CSV.ﾕﾆｯﾄCD = 次年度計画マスタ.適用ユニットコード（適用エリアコードと同レコードのデータ）AND
                // CSV.採算CD7桁 = 次年度計画マスタ.採算管理単位コード（適用エリアコードと同レコードのデータ）　

                try {
                    // LambdaResourceManagerを使用してデータベース操作を実行
                    LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                        // Repository インスタンスを作成（キャッシュしない）
                        JinendoMasterRepository jinendoMasterRepository = new JinendoMasterRepositoryImpl(jdbcTemplate);

                        if (!checkMasterExists("次年度計画マスタ", saisanCode,
                                () -> jinendoMasterRepository.existsBySaisanCode(DateUtil.getNextFiscalYear(), saisanCode, areaCode, groupCode, unitCode))) {
                            errors.add(new ValidationError("saisanCode", saisanCode,
                                    formatMessage(GlobalMessageConstants.ERR_027,"採算管理コード ")));
                        }

                        return null; // 戻り値は不要
                    });

                } catch (Exception e) {
                    logger.error("エリア、採算管理単位コードの権限中にエラーが発生しました", e);
                    errors.add(new ValidationError("database", "connection",
                            "データベース接続エラー: " + e.getMessage()));
                }
            }else
            {
                // 上記以外
                // a.CSV.ｴﾘｱｺｰﾄﾞのチェック
                // パラメータ.エリアコード = CSV.ｴﾘｱｺｰﾄﾞ
                // ifの外にまとめて対応した

                // b.CSV.ｸﾞﾙｰﾌﾟCDのチェック
                // レスポンス.グループコード = CSV.ｸﾞﾙｰﾌﾟCD
                // ifの外にまとめて対応した

                // b.CSV.採算CD7桁のチェック
                // CSV.ｴﾘｱｺｰﾄﾞ = （次年度計画マスタ.適用エリアコード　AND　次年度計画マスタ.年度 = {今年度 ※年度の取得は設計書_共通.xlsxを参照}）AND
                // CSV.ｸﾞﾙｰﾌﾟCD = 次年度計画マスタ.適用グループコード（適用エリアコードと同レコードのデータ）AND
                // CSV.採算CD7桁 = 次年度計画マスタ.採算管理単位コード（適用エリアコードと同レコードのデータ）　
                try {
                    // LambdaResourceManagerを使用してデータベース操作を実行
                    LambdaResourceManager.executeWithJdbcTemplateReadOnly(jdbcTemplate -> {
                        // Repository インスタンスを作成（キャッシュしない）
                        JinendoMasterRepository jinendoMasterRepository = new JinendoMasterRepositoryImpl(jdbcTemplate);

                        if (!checkMasterExists("次年度計画マスタ", saisanCode,
                                () -> jinendoMasterRepository.existsBySaisanCode(DateUtil.getNextFiscalYear(), saisanCode, areaCode, groupCode))) {
                            errors.add(new ValidationError("saisanCode", saisanCode,
                                    formatMessage(GlobalMessageConstants.ERR_027,"採算管理コード ")));
                        }

                        return null; // 戻り値は不要
                    });

                } catch (Exception e) {
                    logger.error("エリア、採算管理単位コードの権限中にエラーが発生しました", e);
                    errors.add(new ValidationError("database", "connection",
                            "データベース接続エラー: " + e.getMessage()));
                }
            }

            if (!hasPermission) {
                errors.add(new ValidationError(fieldName, areaCode, formatMessage(GlobalMessageConstants.ERR_028)));
            }

        } catch (Exception e) {
            logger.error("権限チェック中にエラーが発生しました: areaCode={}, user={}",
                    saisanCode, userInfo.getShainCode(), e);
            errors.add(new ValidationError(fieldName, saisanCode, formatMessage(GlobalMessageConstants.ERR_027, "採算管理コード ")));
        }
    }

    /**
     * マスタデータの存在チェック（汎用メソッド）
     *
     * @param masterName マスタ名（ログ出力用）
     * @param code 検証対象のコード
     * @param existsCheck 存在チェック処理
     * @return 存在する場合true、存在しない場合またはエラーの場合false
     */
    private boolean checkMasterExists(String masterName, String code, Supplier<Boolean> existsCheck) {
        try {
            return existsCheck.get();
        } catch (Exception e) {
            logger.error("{}存在チェック中にエラーが発生しました: code={}", masterName, code, e);
            // エラーが発生した場合は存在しないものとして扱う
            return false;
        }
    }
}
