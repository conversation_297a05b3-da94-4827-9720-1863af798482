package com.ms.bp.domain.file.example;

import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.model.PlanMasterImportData;
import com.ms.bp.shared.common.io.options.DeleteCondition;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 削除-挿入モード使用例
 *
 * 基本的な削除-挿入処理のデモンストレーション：
 * - 年度とエリアの組み合わせで既存データを削除
 * - 新しいデータを挿入
 */
public class PlanDataDeleteInsertImportService extends AbstractImportService<PlanMasterImportData> {
    
    private static final Logger logger = LoggerFactory.getLogger(PlanDataDeleteInsertImportService.class);

    @Override
    protected String getDataType() {
        return "PLAN_DATA_DELETE_INSERT";
    }

    @Override
    protected DataValidator getDataValidator() {
        return new DTODataValidator<>(PlanMasterImportData.class, null);
    }

    @Override
    protected Class<PlanMasterImportData> getDTOClass() {
        return PlanMasterImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        // 基本的なヘッダーマッピング
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("年度", "nendo");
        headerMapping.put("エリアコード", "areaCode");
        headerMapping.put("計画値", "planValue");

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(1000)
                .targetTable("T_JINENDO_KKK")
                // 削除-挿入モードを設定
                .deleteInsertMode((dto, tableName) -> generateDeleteCondition((PlanMasterImportData) dto, tableName))
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName("plan_data_import")
                .enableFieldMapping(true)
                .headerFieldMapping(headerMapping)
                .build();
    }

    /**
     * 削除条件生成ロジック
     * 年度とエリアの組み合わせで既存データを削除
     */
    private DeleteCondition generateDeleteCondition(PlanMasterImportData dto, String tableName) {
        // 年度は次年度を自動取得、エリアコードはDTOから取得
        String nendo = DateUtil.getNextFiscalYear();
        List<String> whereColumns = Arrays.asList("NENDO", "AREA_CODE");
        List<Object> whereValues = Arrays.asList(nendo, dto.getAreaCode());

        logger.debug("削除条件生成: 年度={}, エリア={}", nendo, dto.getAreaCode());

        return new DeleteCondition(whereColumns, whereValues, tableName);
    }

}
