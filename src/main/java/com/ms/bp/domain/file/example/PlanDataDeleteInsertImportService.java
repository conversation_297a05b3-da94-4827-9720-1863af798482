package com.ms.bp.domain.file.example;

import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.model.PlanMasterImportData;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.delete.DeleteCondition;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.util.ExportFileNameUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 計画データインポートサービス（削除-挿入モード使用例）
 * 
 * 使用シナリオ：
 * - 年度とエリアの組み合わせで既存データを完全に置き換える
 * - データタイプに応じて異なる削除条件を適用
 * - バッチ最適化により同じ削除条件をグループ化して処理
 */
@Service
public class PlanDataDeleteInsertImportService extends AbstractImportService<PlanMasterImportData> {
    
    private static final Logger logger = LoggerFactory.getLogger(PlanDataDeleteInsertImportService.class);

    @Override
    protected String getDataType() {
        return "PLAN_DATA_DELETE_INSERT";
    }

    @Override
    protected DataValidator getDataValidator() {
        return new DTODataValidator<>(PlanMasterImportData.class, this::validateCustomLogic);
    }

    @Override
    protected Class<PlanMasterImportData> getDTOClass() {
        return PlanMasterImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        // ヘッダーマッピングを設定
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("年度", "nendo");
        headerMapping.put("採算管理単位コード", "ssnknTncd");
        headerMapping.put("グループコード", "groupCode");
        headerMapping.put("計画値", "planValue");
        headerMapping.put("データタイプ", "dataType");
        headerMapping.put("エリアコード", "areaCode");
        headerMapping.put("月コード", "monthCode");
        headerMapping.put("四半期コード", "quarterCode");
        headerMapping.put("カテゴリコード", "categoryCode");

        // 固定値フィールドを設定
        Map<String, Object> additionalFields = new HashMap<>();
        additionalFields.put("SYSTM_UNYO_KIGYO_CODE", "001");
        additionalFields.put("TRK_SHAIN_CODE", "SYSTEM");

        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(BusinessConstants.CSV_UPLOAD_BATCH_SIZE)
                .targetTable("T_PLAN_DATA")
                // 削除-挿入モードを設定（バッチ最適化有効）
                .deleteInsertMode(this::generateDeleteCondition)
                .skipValidation(false)
                .continueOnError(false)
                .errorFileName(ExportFileNameUtil.getFileNameByFileType("PLAN_DATA"))
                .enableFieldMapping(true)
                .headerFieldMapping(headerMapping)
                .additionalFields(additionalFields)
                .build();
    }

    /**
     * 削除条件生成ロジック
     * 各データ行に基づいて動的に削除条件を決定
     * 
     * 削除条件の例：
     * - 月次データ：年度 + エリア + 月
     * - 四半期データ：年度 + エリア + 四半期  
     * - 年次データ：年度 + エリア
     * - カテゴリ別データ：年度 + エリア + カテゴリ
     */
    private DeleteCondition generateDeleteCondition(PlanMasterImportData dto, ImportOptions options) {
        List<String> whereColumns = new ArrayList<>();
        List<Object> whereValues = new ArrayList<>();

        // 基本的な削除条件：年度 + エリアコード
        whereColumns.add("NENDO");
        whereValues.add(dto.getNendo());
        
        whereColumns.add("AREA_CODE");
        whereValues.add(dto.getAreaCode());

        // データタイプに応じて追加条件を設定
        String dataType = dto.getDataType();
        if ("MONTHLY".equals(dataType)) {
            // 月次データの場合：年度 + エリア + 月
            whereColumns.add("MONTH_CODE");
            whereValues.add(dto.getMonthCode());
            logger.debug("月次データの削除条件生成: 年度={}, エリア={}, 月={}", 
                    dto.getNendo(), dto.getAreaCode(), dto.getMonthCode());
        } else if ("QUARTERLY".equals(dataType)) {
            // 四半期データの場合：年度 + エリア + 四半期
            whereColumns.add("QUARTER_CODE");
            whereValues.add(dto.getQuarterCode());
            logger.debug("四半期データの削除条件生成: 年度={}, エリア={}, 四半期={}", 
                    dto.getNendo(), dto.getAreaCode(), dto.getQuarterCode());
        } else if ("YEARLY".equals(dataType)) {
            // 年次データの場合：年度 + エリアのみ
            logger.debug("年次データの削除条件生成: 年度={}, エリア={}", 
                    dto.getNendo(), dto.getAreaCode());
        }

        // 特定の商品カテゴリがある場合は追加
        if (dto.getCategoryCode() != null && !dto.getCategoryCode().isEmpty()) {
            whereColumns.add("CATEGORY_CODE");
            whereValues.add(dto.getCategoryCode());
            logger.debug("カテゴリ条件追加: カテゴリ={}", dto.getCategoryCode());
        }

        return new DeleteCondition(whereColumns, whereValues);
    }

    /**
     * カスタム検証ロジック
     */
    private List<String> validateCustomLogic(PlanMasterImportData dto) {
        List<String> errors = new ArrayList<>();

        // 年度の妥当性チェック
        if (dto.getNendo() == null || dto.getNendo() < 2020 || dto.getNendo() > 2030) {
            errors.add("年度は2020-2030の範囲で入力してください");
        }

        // データタイプ別の必須項目チェック
        String dataType = dto.getDataType();
        if ("MONTHLY".equals(dataType) && dto.getMonthCode() == null) {
            errors.add("月次データの場合、月コードは必須です");
        } else if ("QUARTERLY".equals(dataType) && dto.getQuarterCode() == null) {
            errors.add("四半期データの場合、四半期コードは必須です");
        }

        // 計画値の妥当性チェック
        if (dto.getPlanValue() != null && dto.getPlanValue() < 0) {
            errors.add("計画値は0以上で入力してください");
        }

        return errors;
    }
}
