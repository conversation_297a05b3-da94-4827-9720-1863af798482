package com.ms.bp.shared.common.io.delete;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * DeleteConditionクラスのテスト
 */
class DeleteConditionTest {

    @Test
    @DisplayName("基本的な削除条件の生成")
    void testBasicDeleteCondition() {
        // Arrange
        List<String> columns = Arrays.asList("NENDO", "AREA_CODE");
        List<Object> values = Arrays.asList(2024, "TOKYO");
        
        // Act
        DeleteCondition condition = new DeleteCondition(columns, values);
        String sql = condition.buildDeleteSql("T_PLAN_DATA");
        
        // Assert
        assertEquals("DELETE FROM T_PLAN_DATA WHERE NENDO = ? AND AREA_CODE = ?", sql);
        assertEquals(Arrays.asList("NENDO", "AREA_CODE"), condition.getWhereColumns());
        assertEquals(Arrays.asList(2024, "TOKYO"), condition.getWhereValues());
    }

    @Test
    @DisplayName("単一条件の削除SQL生成")
    void testSingleConditionDeleteSql() {
        // Arrange
        List<String> columns = Arrays.asList("ID");
        List<Object> values = Arrays.asList(123);
        
        // Act
        DeleteCondition condition = new DeleteCondition(columns, values);
        String sql = condition.buildDeleteSql("T_TEST");
        
        // Assert
        assertEquals("DELETE FROM T_TEST WHERE ID = ?", sql);
    }

    @Test
    @DisplayName("複数条件の削除SQL生成")
    void testMultipleConditionDeleteSql() {
        // Arrange
        List<String> columns = Arrays.asList("YEAR", "MONTH", "CATEGORY");
        List<Object> values = Arrays.asList(2024, 3, "SALES");
        
        // Act
        DeleteCondition condition = new DeleteCondition(columns, values);
        String sql = condition.buildDeleteSql("T_MONTHLY_DATA");
        
        // Assert
        assertEquals("DELETE FROM T_MONTHLY_DATA WHERE YEAR = ? AND MONTH = ? AND CATEGORY = ?", sql);
    }

    @Test
    @DisplayName("空の条件でnullを返す")
    void testEmptyConditionReturnsNull() {
        // Arrange
        List<String> columns = Arrays.asList();
        List<Object> values = Arrays.asList();
        
        // Act
        DeleteCondition condition = new DeleteCondition(columns, values);
        String sql = condition.buildDeleteSql("T_TEST");
        
        // Assert
        assertNull(sql);
    }

    @Test
    @DisplayName("最適化キーの生成")
    void testOptimizationKey() {
        // Arrange
        List<String> columns = Arrays.asList("NENDO", "AREA_CODE", "CATEGORY");
        List<Object> values = Arrays.asList(2024, "TOKYO", "SALES");
        
        // Act
        DeleteCondition condition = new DeleteCondition(columns, values);
        String optimizationKey = condition.getOptimizationKey();
        
        // Assert
        assertEquals("NENDO,AREA_CODE,CATEGORY", optimizationKey);
    }

    @Test
    @DisplayName("列数と値数の不一致でエラー")
    void testMismatchedColumnsAndValues() {
        // Arrange
        List<String> columns = Arrays.asList("NENDO", "AREA_CODE");
        List<Object> values = Arrays.asList(2024); // 値が1つ少ない
        
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new DeleteCondition(columns, values)
        );
        assertEquals("WHERE列数と値の数が一致しません", exception.getMessage());
    }

    @Test
    @DisplayName("リストのコピーが返される")
    void testListCopiesReturned() {
        // Arrange
        List<String> originalColumns = Arrays.asList("NENDO", "AREA_CODE");
        List<Object> originalValues = Arrays.asList(2024, "TOKYO");
        
        // Act
        DeleteCondition condition = new DeleteCondition(originalColumns, originalValues);
        List<String> returnedColumns = condition.getWhereColumns();
        List<Object> returnedValues = condition.getWhereValues();
        
        // Assert
        assertNotSame(originalColumns, returnedColumns);
        assertNotSame(originalValues, returnedValues);
        assertEquals(originalColumns, returnedColumns);
        assertEquals(originalValues, returnedValues);
    }
}
