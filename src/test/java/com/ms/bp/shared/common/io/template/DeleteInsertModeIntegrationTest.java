package com.ms.bp.shared.common.io.template;

import com.ms.bp.shared.common.io.delete.DeleteCondition;
import com.ms.bp.shared.common.io.delete.DeleteConditionProvider;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.infrastructure.external.s3.S3Service;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

/**
 * 削除-挿入モードの統合テスト
 * 
 * 注意：このテストは実際のデータベース接続が必要です
 * テスト環境でのみ実行してください
 */
@SpringBootTest
@ActiveProfiles("test")
class DeleteInsertModeIntegrationTest {

    @Mock
    private S3Service mockS3Service;

    /**
     * テスト用のシンプルなDTO
     */
    public static class TestDTO implements com.ms.bp.shared.common.io.converter.DatabaseMappable {
        private Integer year;
        private String areaCode;
        private String category;
        private Integer value;

        // コンストラクタ
        public TestDTO() {}
        
        public TestDTO(Integer year, String areaCode, String category, Integer value) {
            this.year = year;
            this.areaCode = areaCode;
            this.category = category;
            this.value = value;
        }

        // getters and setters
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
        public String getAreaCode() { return areaCode; }
        public void setAreaCode(String areaCode) { this.areaCode = areaCode; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public Integer getValue() { return value; }
        public void setValue(Integer value) { this.value = value; }

        @Override
        public void toDatabase(Map<String, Object> fields, boolean isInsert, ImportOptions options) {
            fields.put("YEAR", year);
            fields.put("AREA_CODE", areaCode);
            fields.put("CATEGORY", category);
            fields.put("VALUE", value);
        }
    }

    @Test
    @DisplayName("削除-挿入モードの基本動作確認")
    void testDeleteInsertModeBasicOperation() throws Exception {
        // Arrange
        String csvData = "year,areaCode,category,value\n" +
                        "2024,TOKYO,SALES,1000\n" +
                        "2024,OSAKA,SALES,2000\n" +
                        "2024,TOKYO,MARKETING,1500\n";
        
        InputStream inputStream = new ByteArrayInputStream(csvData.getBytes());
        
        // 削除条件プロバイダーを定義
        DeleteConditionProvider<TestDTO> deleteProvider = (dto, options) -> {
            return new DeleteCondition(
                Arrays.asList("YEAR", "AREA_CODE"),
                Arrays.asList(dto.getYear(), dto.getAreaCode())
            );
        };

        ImportOptions options = ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .targetTable("T_TEST_DATA")
                .deleteInsertMode(deleteProvider)
                .batchSize(10)
                .skipValidation(true)
                .build();

        // テスト用のImportTemplateを作成
        ImportTemplate<TestDTO> template = new ImportTemplate<TestDTO>(mockS3Service) {};

        // Act
        ImportResult result = template.executeImport(
                inputStream, 
                TestDTO.class, 
                options, 
                null, // lambdaContext
                mock(DataValidator.class)
        );

        // Assert
        assertNotNull(result);
        assertEquals(3, result.getInsertedCount()); // 3件挿入
        assertEquals(0, result.getUpdatedCount());  // 更新なし
        assertTrue(result.getDeletedCount() >= 0);  // 削除件数（既存データによる）
        assertEquals(0, result.getFailedCount());   // エラーなし
        
        // 統計情報の確認
        assertTrue(result.getStatistics().containsKey("totalProcessed"));
        assertEquals(3, result.getStatistics().get("totalProcessed"));
    }

    @Test
    @DisplayName("条件付き削除の動作確認")
    void testConditionalDeleteOperation() throws Exception {
        // Arrange
        String csvData = "year,areaCode,category,value,mode\n" +
                        "2024,TOKYO,SALES,1000,REPLACE\n" +
                        "2024,OSAKA,SALES,2000,APPEND\n" +
                        "2024,TOKYO,MARKETING,1500,REPLACE\n";
        
        InputStream inputStream = new ByteArrayInputStream(csvData.getBytes());
        
        // 条件付き削除プロバイダー
        DeleteConditionProvider<TestDTO> deleteProvider = (dto, options) -> {
            // REPLACEモードの場合のみ削除を実行
            if ("REPLACE".equals(dto.getCategory())) { // 簡略化のためcategoryフィールドを使用
                return new DeleteCondition(
                    Arrays.asList("YEAR", "AREA_CODE"),
                    Arrays.asList(dto.getYear(), dto.getAreaCode())
                );
            }
            return null; // 削除をスキップ
        };

        ImportOptions options = ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .targetTable("T_TEST_DATA")
                .deleteInsertMode(deleteProvider)
                .batchSize(10)
                .skipValidation(true)
                .build();

        ImportTemplate<TestDTO> template = new ImportTemplate<TestDTO>(mockS3Service) {};

        // Act
        ImportResult result = template.executeImport(
                inputStream, 
                TestDTO.class, 
                options, 
                null,
                mock(DataValidator.class)
        );

        // Assert
        assertNotNull(result);
        assertEquals(3, result.getInsertedCount());
        assertEquals(0, result.getUpdatedCount());
        assertEquals(0, result.getFailedCount());
    }

    @Test
    @DisplayName("バッチ最適化無効モードの動作確認")
    void testSimpleDeleteInsertMode() throws Exception {
        // Arrange
        String csvData = "year,areaCode,category,value\n" +
                        "2024,TOKYO,SALES,1000\n" +
                        "2024,TOKYO,MARKETING,1500\n";
        
        InputStream inputStream = new ByteArrayInputStream(csvData.getBytes());
        
        DeleteConditionProvider<TestDTO> deleteProvider = (dto, options) -> {
            return new DeleteCondition(
                Arrays.asList("YEAR", "AREA_CODE", "CATEGORY"),
                Arrays.asList(dto.getYear(), dto.getAreaCode(), dto.getCategory())
            );
        };

        ImportOptions options = ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .targetTable("T_TEST_DATA")
                .deleteInsertModeSimple(deleteProvider) // バッチ最適化無効
                .batchSize(10)
                .skipValidation(true)
                .build();

        ImportTemplate<TestDTO> template = new ImportTemplate<TestDTO>(mockS3Service) {};

        // Act
        ImportResult result = template.executeImport(
                inputStream, 
                TestDTO.class, 
                options, 
                null,
                mock(DataValidator.class)
        );

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getInsertedCount());
        assertEquals(0, result.getUpdatedCount());
        assertEquals(0, result.getFailedCount());
        assertFalse(options.isBatchDeleteOptimization()); // バッチ最適化が無効
    }
}
