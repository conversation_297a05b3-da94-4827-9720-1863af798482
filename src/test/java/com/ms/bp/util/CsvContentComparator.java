package com.ms.bp.util;

import com.ms.bp.infrastructure.external.s3.S3Service;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static org.assertj.core.api.Assertions.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 汎用CSV内容比較検証器
 * 異なる業務で生成されるZIP文件中のCSV文件内容を汎用的に比較検証するユーティリティクラス
 *
 * 主要機能：
 * 1. S3Service mock呼び出しの確認
 * 2. ローカルからZIP文件を検索・抽出
 * 3. 業務別CSV文件の内容比較（正常データ・異常データ対応）
 * 4. カスタマイズ可能なHeader行とデータ行の詳細検証
 *
 * 使用例：
 * - 次年度計画マスタ、見通し計画、間接利益計画等の各種エクスポート機能のテスト
 * - 正常データ出力時とエラー時の両方のケースに対応
 */
public class CsvContentComparator {

    private static final Logger logger = LoggerFactory.getLogger(CsvContentComparator.class);

    /** デフォルトエラーメッセージ */
    private static final String DEFAULT_ERROR_MESSAGE = "条件に一致するデータが取得できませんでした";

    /**
     * CSV検証設定クラス
     * 業務別のCSV検証パラメータを定義
     */
    public static class CsvValidationConfig {
        private final List<String> expectedHeaders;
        private final String fileNamePrefix;
        private final String zipFilePattern;
        private final String errorMessage;
        private final List<String> expectedAreaCodes;
        private final List<List<String>> expectedDataRows;
        private final List<String> compareFields;
        private final boolean enableDataComparison;
        private final String specificFileName; // 特定ファイル名指定用

        /**
         * 完全CSV検証設定コンストラクタ（データ比較機能付き）
         *
         * @param expectedHeaders 期待されるCSVヘッダーリスト
         * @param fileNamePrefix CSVファイル名のプレフィックス（例：次年度計画マスタ）
         * @param zipFilePattern ZIPファイル名のパターン（例：事業計画）
         * @param errorMessage エラー時の期待メッセージ
         * @param expectedAreaCodes 期待されるエリアコードリスト
         * @param expectedDataRows 期待されるデータ行リスト（全体比較用）
         * @param compareFields 比較対象フィールドリスト（選択比較用、nullの場合は全フィールド）
         * @param specificFileName 特定ファイル名（ZIP内の特定ファイルを指定する場合）
         */
        public CsvValidationConfig(List<String> expectedHeaders, String fileNamePrefix,
                                 String zipFilePattern, String errorMessage, List<String> expectedAreaCodes,
                                 List<List<String>> expectedDataRows, List<String> compareFields, String specificFileName) {
            this.expectedHeaders = expectedHeaders != null ? new ArrayList<>(expectedHeaders) : new ArrayList<>();
            this.fileNamePrefix = fileNamePrefix;
            this.zipFilePattern = zipFilePattern;
            this.errorMessage = errorMessage != null ? errorMessage : DEFAULT_ERROR_MESSAGE;
            this.expectedAreaCodes = expectedAreaCodes != null ? new ArrayList<>(expectedAreaCodes) : new ArrayList<>();
            this.expectedDataRows = expectedDataRows != null ? new ArrayList<>(expectedDataRows) : null;
            this.compareFields = compareFields != null ? new ArrayList<>(compareFields) : null;
            this.enableDataComparison = expectedDataRows != null;
            this.specificFileName = specificFileName;
        }

        /**
         * 完全CSV検証設定コンストラクタ（特定ファイル名なし）
         */
        public CsvValidationConfig(List<String> expectedHeaders, String fileNamePrefix,
                                 String zipFilePattern, String errorMessage, List<String> expectedAreaCodes,
                                 List<List<String>> expectedDataRows, List<String> compareFields) {
            this(expectedHeaders, fileNamePrefix, zipFilePattern, errorMessage, expectedAreaCodes,
                 expectedDataRows, compareFields, null);
        }

        /**
         * データ比較機能付きコンストラクタ（デフォルトエラーメッセージ使用）
         */
        public CsvValidationConfig(List<String> expectedHeaders, String fileNamePrefix,
                                 String zipFilePattern, List<String> expectedAreaCodes,
                                 List<List<String>> expectedDataRows, List<String> compareFields) {
            this(expectedHeaders, fileNamePrefix, zipFilePattern, null, expectedAreaCodes, expectedDataRows, compareFields);
        }

        /**
         * 基本コンストラクタ（データ比較なし）
         */
        public CsvValidationConfig(List<String> expectedHeaders, String fileNamePrefix,
                                 String zipFilePattern, String errorMessage, List<String> expectedAreaCodes) {
            this(expectedHeaders, fileNamePrefix, zipFilePattern, errorMessage, expectedAreaCodes, null, null);
        }

        /**
         * 簡易コンストラクタ（デフォルトエラーメッセージ使用、データ比較なし）
         */
        public CsvValidationConfig(List<String> expectedHeaders, String fileNamePrefix,
                                 String zipFilePattern, List<String> expectedAreaCodes) {
            this(expectedHeaders, fileNamePrefix, zipFilePattern, null, expectedAreaCodes, null, null);
        }

        // Getters
        public List<String> getExpectedHeaders() { return new ArrayList<>(expectedHeaders); }
        public String getFileNamePrefix() { return fileNamePrefix; }
        public String getZipFilePattern() { return zipFilePattern; }
        public String getErrorMessage() { return errorMessage; }
        public List<String> getExpectedAreaCodes() { return new ArrayList<>(expectedAreaCodes); }
        public List<List<String>> getExpectedDataRows() {
            return expectedDataRows != null ? new ArrayList<>(expectedDataRows) : null;
        }
        public List<String> getCompareFields() {
            return compareFields != null ? new ArrayList<>(compareFields) : null;
        }
        public boolean isDataComparisonEnabled() { return enableDataComparison; }
        public String getSpecificFileName() { return specificFileName; }
    }

    /**
     * 次年度計画マスタ用のデフォルトヘッダーを取得
     */
    private static List<String> getPlanMasterHeaders() {
        return Arrays.asList(
            "エリアCD", "エリア名", "カテCD", "カテゴリ", "グループ", "ユニット", "担当者",
            "企業CD", "企業名", "業態集計", "業態名", "サブカテゴリ", "サブカテゴリ名",
            "採算管理単位CD", "採算管理単位名", "変更前取組区分", "変更後取組区分",
            "移管先エリアCD", "移管先エリア名", "移管先グループCD", "移管先ユニットCD",
            "当年度実績累計(参考)", "当年度計画累計(参考)"
        );
    }


    /**
     * 汎用CSV検証設定作成メソッド（特定ファイル名指定版）
     *
     * @param businessName 業務名（例：次年度計画マスタ、見通し計画等）
     * @param headers CSVヘッダーリスト
     * @param zipFilePattern ZIPファイル名パターン
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectedDataRows 期待されるデータ行リスト（nullの場合は基本検証のみ）
     * @param compareFields 比較対象フィールドリスト（nullの場合は全フィールド比較）
     * @param errorMessage カスタムエラーメッセージ（nullの場合はデフォルト）
     * @param specificFileName 特定ファイル名（ZIP内の特定ファイルを指定、nullの場合はプレフィックス検索）
     * @return CSV検証設定
     */
    public static CsvValidationConfig createCsvValidationConfig(
            String businessName,
            List<String> headers,
            String zipFilePattern,
            List<String> expectedAreaCodes,
            List<List<String>> expectedDataRows,
            List<String> compareFields,
            String errorMessage,
            String specificFileName) {

        return new CsvValidationConfig(
            headers,
            businessName,
            zipFilePattern,
            errorMessage,
            expectedAreaCodes,
            expectedDataRows,
            compareFields,
            specificFileName
        );
    }

    /**
     * 汎用CSV検証設定作成メソッド（特定ファイル名なし）
     */
    public static CsvValidationConfig createCsvValidationConfig(
            String businessName,
            List<String> headers,
            String zipFilePattern,
            List<String> expectedAreaCodes,
            List<List<String>> expectedDataRows,
            List<String> compareFields,
            String errorMessage) {

        return createCsvValidationConfig(
            businessName, headers, zipFilePattern, expectedAreaCodes,
            expectedDataRows, compareFields, errorMessage, null
        );
    }

    /**
     * 次年度計画マスタ用のデフォルト設定を作成
     */
    public static CsvValidationConfig createPlanMasterConfig(List<String> expectedAreaCodes) {
        return createCsvValidationConfig(
            "次年度計画マスタ",
            getPlanMasterHeaders(),
            "事業計画",
            expectedAreaCodes,
            null, // データ比較なし
            null, // 全フィールド
            null  // デフォルトエラーメッセージ
        );
    }

    /**
     * データ比較機能付き次年度計画マスタ設定を作成
     */
    public static CsvValidationConfig createPlanMasterConfigWithData(List<String> expectedAreaCodes,
                                                                   List<List<String>> expectedDataRows) {
        return createCsvValidationConfig(
            "次年度計画マスタ",
            getPlanMasterHeaders(),
            "事業計画",
            expectedAreaCodes,
            expectedDataRows,
            null, // 全フィールド比較
            null  // デフォルトエラーメッセージ
        );
    }

    /**
     * 選択フィールド比較機能付き次年度計画マスタ設定を作成
     */
    public static CsvValidationConfig createPlanMasterConfigWithSelectedFields(List<String> expectedAreaCodes,
                                                                             List<List<String>> expectedDataRows,
                                                                             List<String> compareFields) {
        return createCsvValidationConfig(
            "次年度計画マスタ",
            getPlanMasterHeaders(),
            "事業計画",
            expectedAreaCodes,
            expectedDataRows,
            compareFields,
            null  // デフォルトエラーメッセージ
        );
    }

    /**
     * 汎用データ比較設定作成ヘルパー
     * 任意の業務用のデータ比較設定を作成
     *
     * @param headers CSVヘッダーリスト
     * @param fileNamePrefix CSVファイル名プレフィックス
     * @param zipFilePattern ZIPファイル名パターン
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectedDataRows 期待されるデータ行リスト
     * @return データ比較機能付きCSV検証設定
     */
    public static CsvValidationConfig createDataComparisonConfig(List<String> headers, String fileNamePrefix,
                                                               String zipFilePattern, List<String> expectedAreaCodes,
                                                               List<List<String>> expectedDataRows) {
        return new CsvValidationConfig(
            headers,
            fileNamePrefix,
            zipFilePattern,
            expectedAreaCodes,
            expectedDataRows,
            null // 全フィールド比較
        );
    }

    /**
     * 汎用選択フィールド比較設定作成ヘルパー
     * 任意の業務用の選択フィールド比較設定を作成
     *
     * @param headers CSVヘッダーリスト
     * @param fileNamePrefix CSVファイル名プレフィックス
     * @param zipFilePattern ZIPファイル名パターン
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectedDataRows 期待されるデータ行リスト
     * @param compareFields 比較対象フィールドリスト
     * @return 選択フィールド比較機能付きCSV検証設定
     */
    public static CsvValidationConfig createFieldComparisonConfig(List<String> headers, String fileNamePrefix,
                                                                String zipFilePattern, List<String> expectedAreaCodes,
                                                                List<List<String>> expectedDataRows, List<String> compareFields) {
        return new CsvValidationConfig(
            headers,
            fileNamePrefix,
            zipFilePattern,
            expectedAreaCodes,
            expectedDataRows,
            compareFields
        );
    }

    /**
     * 特定ファイル名指定用の設定作成ヘルパー
     * ZIP内の特定ファイルを指定してCSV検証設定を作成
     *
     * @param businessName 業務名
     * @param headers CSVヘッダーリスト
     * @param zipFilePattern ZIPファイル名パターン
     * @param specificFileName 特定ファイル名（ZIP内の対象ファイル）
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectedDataRows 期待されるデータ行リスト
     * @param compareFields 比較対象フィールドリスト
     * @return 特定ファイル指定CSV検証設定
     */
    public static CsvValidationConfig createSpecificFileConfig(
            String businessName,
            List<String> headers,
            String zipFilePattern,
            String specificFileName,
            List<String> expectedAreaCodes,
            List<List<String>> expectedDataRows,
            List<String> compareFields) {

        return createCsvValidationConfig(
            businessName, headers, zipFilePattern, expectedAreaCodes,
            expectedDataRows, compareFields, null, specificFileName
        );
    }

    /**
     * 複数ファイル検証用のヘルパークラス
     * ZIP内の複数CSVファイルを一括検証するための設定管理
     */
    public static class MultiFileValidationConfig {
        private final String zipFilePattern;
        private final List<CsvValidationConfig> fileConfigs;

        public MultiFileValidationConfig(String zipFilePattern, List<CsvValidationConfig> fileConfigs) {
            this.zipFilePattern = zipFilePattern;
            this.fileConfigs = new ArrayList<>(fileConfigs);
        }

        public String getZipFilePattern() { return zipFilePattern; }
        public List<CsvValidationConfig> getFileConfigs() { return new ArrayList<>(fileConfigs); }
    }

    /**
     * 複数ファイル検証実行メソッド
     * ZIP内の複数CSVファイルを一括で検証
     *
     * @param mockS3Service MockされたS3Service
     * @param multiConfig 複数ファイル検証設定
     */
    public static void validateMultipleCsvFiles(S3Service mockS3Service, MultiFileValidationConfig multiConfig) {
        logger.info("=== 複数CSVファイル検証開始：ZIPパターン={} ===", multiConfig.getZipFilePattern());

        try {
            // S3Service mock呼び出し確認
            verifyS3ServiceCalled(mockS3Service);

            // ZIPファイルを取得
            Path zipFilePath = findGeneratedZipFile(multiConfig.getZipFilePattern());
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);

            logger.info("ZIP内のCSVファイル一覧: {}", csvContents.keySet());

            // 各ファイル設定に対して検証を実行
            for (int i = 0; i < multiConfig.getFileConfigs().size(); i++) {
                CsvValidationConfig config = multiConfig.getFileConfigs().get(i);

                logger.info("ファイル検証開始 [{}/{}]: 業務={}, ファイル指定={}",
                           i + 1, multiConfig.getFileConfigs().size(),
                           config.getFileNamePrefix(),
                           config.getSpecificFileName() != null ? config.getSpecificFileName() : "プレフィックス検索");

                try {
                    // 対象CSVファイルを特定
                    String targetCsvContent = findTargetCsvContent(csvContents, config);

                    // データ比較が有効な場合
                    if (config.isDataComparisonEnabled()) {
                        if (config.getCompareFields() != null && !config.getCompareFields().isEmpty()) {
                            // 選択フィールド比較
                            compareSelectedCsvFields(targetCsvContent, config);
                        } else {
                            // 全体データ比較
                            compareCsvData(targetCsvContent, config);
                        }
                    } else {
                        // 基本構造検証
                        validateCsvContent(targetCsvContent, config);
                    }

                    logger.info("ファイル検証完了 [{}/{}]: 業務={}",
                               i + 1, multiConfig.getFileConfigs().size(), config.getFileNamePrefix());

                } catch (Exception e) {
                    logger.error("ファイル検証エラー [{}/{}]: 業務={}, エラー={}",
                                i + 1, multiConfig.getFileConfigs().size(),
                                config.getFileNamePrefix(), e.getMessage());
                    throw new RuntimeException("複数ファイル検証でエラーが発生しました: " + config.getFileNamePrefix(), e);
                }
            }

            logger.info("=== 複数CSVファイル検証完了：全{}ファイルの検証が正常 ===", multiConfig.getFileConfigs().size());

        } catch (Exception e) {
            logger.error("複数CSVファイル検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("複数CSVファイル検証に失敗しました", e);
        }
    }
    
    /**
     * 汎用CSV内容検証（メインメソッド）
     * 設定に基づいて生成されたZIPファイルからCSV内容を検証
     *
     * @param mockS3Service MockされたS3Service（S3呼び出し確認用）
     * @param config CSV検証設定
     */
    public static void validateCsvContentFromS3Mock(S3Service mockS3Service, CsvValidationConfig config) {
        logger.info("=== 汎用CSV内容比較検証開始：業務={} ===", config.getFileNamePrefix());

        try {
            // 1. S3Service mock呼び出し確認（簡化版）
            verifyS3ServiceCalled(mockS3Service);

            // 2. ローカル一時ディレクトリからZIPファイルを検索
            Path zipFilePath = findGeneratedZipFile(config.getZipFilePattern());

            // 3. ZIP文件からCSV文件を抽出
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);

            // 4. 対象CSVファイルを特定
            String targetCsvContent = findTargetCsvContent(csvContents, config);

            // 5. CSV内容の詳細検証
            validateCsvContent(targetCsvContent, config);

            logger.info("=== 汎用CSV内容比較検証完了：業務={} ===", config.getFileNamePrefix());

        } catch (Exception e) {
            logger.error("CSV内容比較検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("CSV内容検証に失敗しました", e);
        }
    }

    /**
     * 次年度計画マスタ専用の便利メソッド（後方互換性のため）
     *
     * @param mockS3Service MockされたS3Service
     * @param expectedAreaCodes 期待されるエリアコードリスト
     */
    public static void validateCsvContentFromS3Mock(S3Service mockS3Service, List<String> expectedAreaCodes) {
        CsvValidationConfig config = createPlanMasterConfig(expectedAreaCodes);
        validateCsvContentFromS3Mock(mockS3Service, config);
    }

    /**
     * 正常データCSV検証専用メソッド
     * データが正常に出力された場合のCSV内容を検証
     *
     * @param mockS3Service MockされたS3Service
     * @param config CSV検証設定
     */
    public static void validateNormalCsvContent(S3Service mockS3Service, CsvValidationConfig config) {
        logger.info("=== 正常データCSV検証開始：業務={} ===", config.getFileNamePrefix());

        try {
            verifyS3ServiceCalled(mockS3Service);
            Path zipFilePath = findGeneratedZipFile(config.getZipFilePattern());
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);
            String targetCsvContent = findTargetCsvContent(csvContents, config);

            // 正常データの場合はエラーファイルでないことを確認
            if (targetCsvContent.contains(config.getErrorMessage())) {
                throw new RuntimeException("正常データ検証でエラーCSVが検出されました: " + config.getErrorMessage());
            }

            validateNormalCsvStructure(targetCsvContent, config);

            logger.info("=== 正常データCSV検証完了：業務={} ===", config.getFileNamePrefix());

        } catch (Exception e) {
            logger.error("正常データCSV検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("正常データCSV検証に失敗しました", e);
        }
    }

    /**
     * 異常データCSV検証専用メソッド
     * データが取得できなかった場合のエラーCSV内容を検証
     *
     * @param mockS3Service MockされたS3Service
     * @param config CSV検証設定
     */
    public static void validateErrorCsvContent(S3Service mockS3Service, CsvValidationConfig config) {
        logger.info("=== 異常データCSV検証開始：業務={} ===", config.getFileNamePrefix());

        try {
            verifyS3ServiceCalled(mockS3Service);
            Path zipFilePath = findGeneratedZipFile(config.getZipFilePattern());
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);
            String targetCsvContent = findTargetCsvContent(csvContents, config);

            // エラーCSVの場合はエラーメッセージが含まれることを確認
            if (!targetCsvContent.contains(config.getErrorMessage())) {
                throw new RuntimeException("異常データ検証でエラーメッセージが見つかりません: " + config.getErrorMessage());
            }

            // CSV行に分割して最初の行（エラーメッセージ行）を取得
            String[] csvLines = targetCsvContent.split("\n");
            String errorLine = csvLines.length > 0 ? csvLines[0] : targetCsvContent;

            validateErrorCsvStructure(errorLine, config);

            logger.info("=== 異常データCSV検証完了：業務={} ===", config.getFileNamePrefix());

        } catch (Exception e) {
            logger.error("異常データCSV検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("異常データCSV検証に失敗しました", e);
        }
    }

    /**
     * CSV全体データ比較検証
     * 期待されるCSVデータと実際のCSVデータを全体的に比較
     *
     * @param mockS3Service MockされたS3Service
     * @param config CSV検証設定（期待データを含む）
     */
    public static void validateCsvDataComparison(S3Service mockS3Service, CsvValidationConfig config) {
        logger.info("=== CSV全体データ比較検証開始：業務={} ===", config.getFileNamePrefix());

        if (!config.isDataComparisonEnabled()) {
            throw new IllegalArgumentException("データ比較が有効になっていません。期待データを設定してください。");
        }

        try {
            verifyS3ServiceCalled(mockS3Service);
            Path zipFilePath = findGeneratedZipFile(config.getZipFilePattern());
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);
            String targetCsvContent = findTargetCsvContent(csvContents, config);

            // エラーCSVでないことを確認
            if (targetCsvContent.contains(config.getErrorMessage())) {
                throw new RuntimeException("データ比較検証でエラーCSVが検出されました: " + config.getErrorMessage());
            }

            // CSV全体データ比較を実行
            compareCsvData(targetCsvContent, config);

            logger.info("=== CSV全体データ比較検証完了：業務={} ===", config.getFileNamePrefix());

        } catch (Exception e) {
            logger.error("CSV全体データ比較検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("CSV全体データ比較検証に失敗しました", e);
        }
    }

    /**
     * CSV選択フィールド比較検証
     * 指定されたフィールドのみを対象としてCSVデータを比較
     *
     * @param mockS3Service MockされたS3Service
     * @param config CSV検証設定（期待データと比較フィールドを含む）
     */
    public static void validateCsvFieldComparison(S3Service mockS3Service, CsvValidationConfig config) {
        logger.info("=== CSV選択フィールド比較検証開始：業務={} ===", config.getFileNamePrefix());

        if (!config.isDataComparisonEnabled()) {
            throw new IllegalArgumentException("データ比較が有効になっていません。期待データを設定してください。");
        }

        if (config.getCompareFields() == null || config.getCompareFields().isEmpty()) {
            throw new IllegalArgumentException("比較フィールドが指定されていません。");
        }

        try {
            verifyS3ServiceCalled(mockS3Service);
            Path zipFilePath = findGeneratedZipFile(config.getZipFilePattern());
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);
            String targetCsvContent = findTargetCsvContent(csvContents, config);

            // エラーCSVでないことを確認
            if (targetCsvContent.contains(config.getErrorMessage())) {
                throw new RuntimeException("フィールド比較検証でエラーCSVが検出されました: " + config.getErrorMessage());
            }

            // CSV選択フィールド比較を実行
            compareSelectedCsvFields(targetCsvContent, config);

            logger.info("=== CSV選択フィールド比較検証完了：業務={} ===", config.getFileNamePrefix());

        } catch (Exception e) {
            logger.error("CSV選択フィールド比較検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("CSV選択フィールド比較検証に失敗しました", e);
        }
    }
    
    /**
     * S3Service呼び出し確認（簡化版）
     * Mock呼び出しが実行されたことのみを確認
     */
    private static void verifyS3ServiceCalled(S3Service mockS3Service) {
        logger.debug("S3Service呼び出し確認開始");
        
        try {
            // S3アップロードが実行されたことを確認（詳細パラメータは検証しない）
            verify(mockS3Service, atLeastOnce())
                .uploadFileFromStream(
                    any(InputStream.class),
                    anyString(),
                    anyString(),
                    anyLong(),
                    any()
                );
            
            logger.debug("S3Service呼び出し確認完了");
            
        } catch (Exception e) {
            logger.error("S3Service呼び出し確認エラー: {}", e.getMessage(), e);
            throw new RuntimeException("S3Service呼び出し確認に失敗しました", e);
        }
    }
    
    /**
     * ローカル一時ディレクトリから生成されたZIPファイルを検索
     * システム一時ディレクトリ内のZIPファイルを検索し、指定パターンのZIPファイルを特定
     *
     * @param zipFilePattern ZIPファイル名のパターン（例：事業計画、見通し計画等）
     */
    private static Path findGeneratedZipFile(String zipFilePattern) {
        logger.debug("生成されたZIPファイル検索開始: パターン={}", zipFilePattern);

        try {
            // システム一時ディレクトリを取得
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            logger.debug("一時ディレクトリ: {}", tempDir);

            // ZIPファイル名パターンを構築（パターン_YYYYMMDDHHMMss.zip または パターン_null.zip）
            String zipPattern = zipFilePattern + "_(\\d{12}|null)\\.zip";
            logger.debug("検索パターン: {}", zipPattern);

            // 段階2: zip_temp*ディレクトリ内を詳細検索
            logger.debug("段階2: zip_temp*ディレクトリ内詳細検索開始");
            try (var stream = Files.walk(tempDir)) {
                java.util.List<Path> zipTempDirs = stream
                        .filter(Files::isDirectory)
                        .filter(path -> path.getFileName().toString().startsWith("zip_temp"))
                        .map(path -> {
                            try {
                                // ファイルの作成時間を取得する
                                BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
                                Instant creationTime = attrs.creationTime().toInstant();
                                return new FileWithTime(path, creationTime);
                            } catch (IOException e) {
                                System.err.println("ファイルの属性を読み取ることができません: " + path + ", エラーメッセージ: " + e.getMessage());
                                return null;
                            }
                        })
                        .filter(fileWithTime -> fileWithTime != null)
                        .sorted((f1, f2) -> f2.creationTime.compareTo(f1.creationTime))  // 作成時間で降順にソートする
                        .map(fileWithTime -> fileWithTime.path)  // Pathを抽出する
                        .collect(Collectors.toList());

                logger.debug("発見されたzip_tempディレクトリ数: {}", zipTempDirs.size());
                for (Path zipTempDir : zipTempDirs) {
                    logger.debug("zip_tempディレクトリ検索: {}", zipTempDir);

                    try (var zipStream = Files.walk(zipTempDir)) {
                        java.util.Optional<Path> zipFile = zipStream
                            .filter(Files::isRegularFile)
                            .filter(path -> path.getFileName().toString().matches(zipPattern))
                            .findFirst();

                        if (zipFile.isPresent()) {
                            logger.debug("ZIPファイル発見（zip_tempディレクトリ内）: {}", zipFile.get());
                            return zipFile.get();
                        }
                    }
                }
            }
            // 見つからない場合は詳細なデバッグ情報を出力
            logger.error("指定パターンのZIPファイルが見つかりません。");
            logger.error("検索パターン: {}", zipPattern);
            logger.error("一時ディレクトリ: {}", tempDir);

            throw new RuntimeException("指定パターンのZIPファイルが見つかりません: " + zipPattern);

        } catch (Exception e) {
            logger.error("ZIPファイル検索エラー: {}", e.getMessage(), e);
            throw new RuntimeException("ZIPファイル検索に失敗しました", e);
        }
    }

    /**
     * ローカル一時目录から生成されたtempファイルを検索
     * システム一時ディレクトリ内のZIPファイルを検索し、指定パターンのtempファイルを特定
     * @param prefix_folder
     * @param prefix_file
     */
    private static Path findLatestFile(String prefix_folder,String prefix_file) {
        logger.debug("指定ファイル検索開始: フォルダ={}、ファイル={}",prefix_folder, prefix_file);

        try {
            // システム一時ディレクトリを取得
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            logger.debug("一時ディレクトリ: {}", tempDir);
            logger.debug("検索フォルダ：{} 、検索ファイル: {}", prefix_folder,prefix_file);

            try (var stream = Files.walk(tempDir)) {
                java.util.List<Path> tempDirs = stream
                        .filter(Files::isDirectory)
                        .filter(path -> path.getFileName().toString().startsWith(prefix_folder))
                        .map(path -> {
                            try {
                                // ファイルの作成時間を取得する
                                BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
                                Instant creationTime = attrs.creationTime().toInstant();
                                return new FileWithTime(path, creationTime);
                            } catch (IOException e) {
                                System.err.println("ファイルの属性を読み取ることができません: " + path + ", エラーメッセージ: " + e.getMessage());
                                return null;
                            }
                        })
                        .filter(fileWithTime -> fileWithTime != null)
                        .sorted((f1, f2) -> f2.creationTime.compareTo(f1.creationTime))  // 作成時間で降順にソートする
                        .map(fileWithTime -> fileWithTime.path)  // Pathを抽出する
                        .collect(Collectors.toList());

                logger.debug("発見された「{}」フォルダ数: {}", prefix_folder,tempDirs.size());
                for (Path dir : tempDirs) {
                    logger.debug("フォルダ検索: {}", dir);

                    try (var dirStream  = Files.walk(dir,1)) {
                        Optional<Path> fileOptional = dirStream
                                .filter(Files::isRegularFile)
                                .filter(path -> path.getFileName().toString().startsWith(prefix_file))
                                .findFirst();

                        if (fileOptional.isPresent()) {
                            logger.debug("指定ファイル発見（{}フォルダ内）: {}", prefix_folder,fileOptional.get());
                            return fileOptional.get();
                        }
                    }
                }
            }
            // 見つからない場合は詳細なデバッグ情報を出力
            logger.error("指定ファイルが見つかりません。");
            logger.error("検索ファイル: {}", prefix_file);
            logger.error("一時ディレクトリ: {}", tempDir);

            throw new RuntimeException("指定ファイルが見つかりません: " + prefix_file);

        } catch (Exception e) {
            logger.error("指定ファイル検索エラー: {}", e.getMessage(), e);
            throw new RuntimeException("指定ファイル検索に失敗しました", e);
        }
    }

    /**
     * ファイルのパスと作成時間を保存する
     */
    private static class FileWithTime {
        Path path;
        Instant creationTime;

        FileWithTime(Path path, Instant creationTime) {
            this.path = path;
            this.creationTime = creationTime;
        }
    }
    /**
     * 後方互換性のためのオーバーロードメソッド
     * 次年度計画マスタ用のデフォルトパターンを使用
     */
    private static Path findGeneratedZipFile() {
        return findGeneratedZipFile("事業計画");
    }

    /**
     * ローカルTxtファイルから抽出
     */
    private static List<String> getTxtFileContent(Path filePath) {
        logger.debug("ローカルTxtファイルから抽出開始: {}", filePath);

        try {
            List<String> lines = Files.readAllLines(filePath, StandardCharsets.UTF_8);
            return lines;

        } catch (Exception e) {
            logger.error("ローカルTxtファイルから抽出エラー: {}", e.getMessage(), e);
            throw new RuntimeException("ローカルTxtファイルから抽出に失敗しました", e);
        }
    }

    /**
     * ローカルTxtファイルパースを検出と内容取得
     */
    public static List<String> findTxtFileAndGetContent(String prefix_folder, String prefix_file) {
        logger.debug("ローカルTxtファイルパースを検出と内容取得開始:フォルダ：{}、ファイル：{}", prefix_folder,prefix_file);
        Path path=findLatestFile(prefix_folder,prefix_file);
        try {
            List<String> lines = Files.readAllLines(path, StandardCharsets.UTF_8);
            System.out.println(lines);
            return lines;

        } catch (Exception e) {
            logger.error("ローカルTxtファイルパースを検出と内容取得エラー: {}", e.getMessage(), e);
            throw new RuntimeException("ローカルTxtファイルパースを検出と内容取得に失敗しました", e);
        }
    }

    /**
     * ローカルZIPファイルからCSV文件を抽出
     * ZIP内の全てのCSVファイルを読み取り、ファイル名と内容のマップを返却
     */
    private static Map<String, String> extractCsvFilesFromZipFile(Path zipFilePath) {
        logger.debug("ローカルZIPファイルからCSV抽出開始: {}", zipFilePath);

        Map<String, String> csvContents = new LinkedHashMap<>();

        try (FileInputStream fis = new FileInputStream(zipFilePath.toFile());
             ZipInputStream zis = new ZipInputStream(fis)) {

            ZipEntry entry;

            while ((entry = zis.getNextEntry()) != null) {
                String fileName = entry.getName();

                // CSVファイルのみを処理
                if (fileName.toLowerCase().endsWith(".csv")) {
                    logger.debug("CSV文件発見: {}", fileName);

                    // CSV内容を読み取り
                    StringBuilder csvContent = new StringBuilder();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(zis, "UTF-8"));
                    String line;

                    while ((line = reader.readLine()) != null) {
                        csvContent.append(line).append("\n");
                    }

                    csvContents.put(fileName, csvContent.toString());
                    logger.debug("CSV文件読み取り完了: {} ({}行)", fileName, csvContent.toString().split("\n").length);
                }

                zis.closeEntry();
            }

            logger.debug("ローカルZIPファイルからCSV抽出完了: {}個のCSVファイル", csvContents.size());
            return csvContents;

        } catch (Exception e) {
            logger.error("ローカルZIPファイルからCSV抽出エラー: {}", e.getMessage(), e);
            throw new RuntimeException("ローカルZIPファイルからCSV抽出に失敗しました", e);
        }
    }
    
    /**
     * 対象CSVファイルを特定
     * 特定ファイル名またはプレフィックスパターンマッチングでCSVファイルを特定
     *
     * @param csvContents ZIP内のCSVファイル内容マップ
     * @param config CSV検証設定
     */
    private static String findTargetCsvContent(Map<String, String> csvContents, CsvValidationConfig config) {
        String specificFileName = config.getSpecificFileName();
        String fileNamePrefix = config.getFileNamePrefix();

        if (specificFileName != null && !specificFileName.isEmpty()) {
            // 特定ファイル名が指定されている場合
            logger.debug("特定ファイル名で検索開始: ファイル名={}", specificFileName);

            for (Map.Entry<String, String> entry : csvContents.entrySet()) {
                String fileName = entry.getKey();

                if (fileName.equals(specificFileName) || fileName.contains(specificFileName)) {
                    logger.debug("特定ファイル名で検索完了: ファイル名={}", fileName);
                    return entry.getValue();
                }
            }

            // 特定ファイル名で見つからない場合はエラー
            logger.error("指定ファイル名のCSVファイルが見つかりません。ファイル名: {}, 利用可能ファイル: {}",
                        specificFileName, csvContents.keySet());
            throw new RuntimeException("指定ファイル名のCSVファイルが見つかりません: " + specificFileName);

        } else {
            // プレフィックスパターンマッチング（従来の方法）
            logger.debug("プレフィックスパターンで検索開始: プレフィックス={}", fileNamePrefix);

            // ファイル名パターン：プレフィックス_YYYYMMDDHHMMSS.csv または error_プレフィックス_YYYYMMDDHHMMSS.csv
            String csvPattern = ".*" + fileNamePrefix + ".*\\.csv";

            for (Map.Entry<String, String> entry : csvContents.entrySet()) {
                String fileName = entry.getKey();

                if (fileName.matches(csvPattern)) {
                    logger.debug("プレフィックスパターンで検索完了: ファイル名={}", fileName);
                    return entry.getValue();
                }
            }

            // 見つからない場合はエラー
            logger.error("指定プレフィックスのCSVファイルが見つかりません。プレフィックス: {}, 利用可能ファイル: {}",
                        fileNamePrefix, csvContents.keySet());
            throw new RuntimeException("指定プレフィックスのCSVファイルが見つかりません: " + fileNamePrefix);
        }
    }

    /**
     * 対象CSVファイルを特定（後方互換性用）
     * プレフィックスパターンマッチングでCSVファイルを特定
     *
     * @param csvContents ZIP内のCSVファイル内容マップ
     * @param fileNamePrefix CSVファイル名のプレフィックス
     */
    private static String findTargetCsvContent(Map<String, String> csvContents, String fileNamePrefix) {
        // 後方互換性のため、プレフィックスのみの設定を作成
        CsvValidationConfig tempConfig = createCsvValidationConfig(
            fileNamePrefix, Arrays.asList(), "", Arrays.asList(), null, null, null, null
        );
        return findTargetCsvContent(csvContents, tempConfig);
    }

    /**
     * 後方互換性のためのメソッド
     * 次年度計画マスタ用のデフォルトプレフィックスを使用
     */
    private static String findPlanMasterCsvContent(Map<String, String> csvContents) {
        return findTargetCsvContent(csvContents, "次年度計画マスタ");
    }
    
    /**
     * CSV内容の詳細検証（汎用版）
     * 設定に基づいてHeader行とデータ行の内容を詳細に検証
     * エラーCSVファイル（データなし）の場合は特別な検証を実行
     */
    private static void validateCsvContent(String csvContent, CsvValidationConfig config) {
        logger.debug("CSV内容詳細検証開始: 業務={}", config.getFileNamePrefix());

        // CSV行に分割
        String[] csvLines = csvContent.split("\n");

        // 基本構造検証
        assertThat(csvLines)
            .as("CSV行数が1行以上であること")
            .hasSizeGreaterThanOrEqualTo(1);

        // エラーCSVファイル（データなし）の場合の特別処理
        if (csvLines.length == 1 && csvLines[0].contains(config.getErrorMessage())) {
            logger.info("エラーCSVファイルを検出: データなしの場合の検証を実行");
            validateErrorCsvStructure(csvLines[0], config);
            return;
        }

        // 通常のCSVファイルの場合
        validateNormalCsvStructure(csvContent, config);

        logger.debug("CSV内容詳細検証完了: 業務={}", config.getFileNamePrefix());
    }

    /**
     * 正常データCSV構造検証
     * Header行とデータ行の詳細検証を実行
     */
    private static void validateNormalCsvStructure(String csvContent, CsvValidationConfig config) {
        logger.debug("正常データCSV構造検証開始: 業務={}", config.getFileNamePrefix());

        String[] csvLines = csvContent.split("\n");

        // Header行検証
        validateCsvHeader(csvLines[0], config.getExpectedHeaders());

        // データ行検証
        if (csvLines.length > 1) {
            validateCsvDataRows(Arrays.copyOfRange(csvLines, 1, csvLines.length),
                              config.getExpectedAreaCodes(), config.getExpectedHeaders());
        } else {
            logger.warn("CSVデータ行が存在しません（ヘッダーのみ）");
        }

        logger.debug("正常データCSV構造検証完了: 業務={}", config.getFileNamePrefix());
    }

    /**
     * エラーCSV構造検証
     * データが取得できなかった場合のCSVファイル内容を検証
     */
    private static void validateErrorCsvStructure(String errorLine, CsvValidationConfig config) {
        logger.debug("エラーCSV構造検証開始: 業務={}", config.getFileNamePrefix());

        // エラーメッセージの検証
        assertThat(errorLine)
            .as("エラーCSVファイルに適切なメッセージが含まれること")
            .contains(config.getErrorMessage());

        logger.info("エラーCSV構造検証完了: データなしの状態が正常に処理されています");
        logger.warn("注意: テストデータまたはSQL条件を確認してください。実際のデータが取得されていません。");
    }

    /**
     * 後方互換性のためのメソッド
     * 次年度計画マスタ用のデフォルト設定を使用
     */
    private static void validateCsvContent(String csvContent, List<String> expectedAreaCodes) {
        CsvValidationConfig config = createPlanMasterConfig(expectedAreaCodes);
        validateCsvContent(csvContent, config);
    }
    
    /**
     * CSVヘッダー行検証（汎用版）
     * 指定されたヘッダーリストに基づいてフィールドの名称と順序を確認
     *
     * @param headerLine CSVヘッダー行
     * @param expectedHeaders 期待されるヘッダーリスト
     */
    private static void validateCsvHeader(String headerLine, List<String> expectedHeaders) {
        logger.debug("CSVヘッダー検証開始: 期待フィールド数={}", expectedHeaders.size());

        String[] actualHeaders = headerLine.split(",");

        // ヘッダー数の検証
        assertThat(actualHeaders)
            .as("ヘッダーフィールド数が期待値と一致すること")
            .hasSize(expectedHeaders.size());

        // ヘッダー名称と順序の検証
        for (int i = 0; i < expectedHeaders.size(); i++) {
            String expectedHeader = expectedHeaders.get(i);
            String actualHeader = actualHeaders[i].trim().replace("\"", "");

            assertThat(actualHeader)
                .as("ヘッダー位置%dの名称が期待値と一致すること", i + 1)
                .isEqualTo(expectedHeader);
        }

        logger.debug("CSVヘッダー検証完了: {}フィールド確認", expectedHeaders.size());
    }

    /**
     * 後方互換性のためのメソッド
     * 次年度計画マスタ用のデフォルトヘッダーを使用
     */
    private static void validateCsvHeader(String headerLine) {
        List<String> defaultHeaders = createPlanMasterConfig(Arrays.asList()).getExpectedHeaders();
        validateCsvHeader(headerLine, defaultHeaders);
    }
    
    /**
     * CSVデータ行検証（汎用版）
     * 各データ行の基本的な形式を検証
     *
     * @param dataLines データ行配列
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectedHeaders 期待されるヘッダーリスト
     */
    private static void validateCsvDataRows(String[] dataLines, List<String> expectedAreaCodes, List<String> expectedHeaders) {
        logger.debug("CSVデータ行検証開始: {}行", dataLines.length);

        for (int i = 0; i < dataLines.length; i++) {
            String dataLine = dataLines[i].trim();

            if (dataLine.isEmpty()) {
                continue; // 空行はスキップ
            }

            String[] fields = dataLine.split(",");

            // フィールド数検証
            assertThat(fields)
                .as("データ行%dのフィールド数が期待値と一致すること", i + 1)
                .hasSize(expectedHeaders.size());

            // 基本的なデータ形式検証
            validateDataRowFormat(fields, i + 1, expectedHeaders);
        }

        logger.debug("CSVデータ行検証完了: {}行の検証完了", dataLines.length);
    }

    /**
     * 後方互換性のためのメソッド
     * 次年度計画マスタ用のデフォルトヘッダーを使用
     */
    private static void validateCsvDataRows(String[] dataLines, List<String> expectedAreaCodes) {
        List<String> defaultHeaders = createPlanMasterConfig(Arrays.asList()).getExpectedHeaders();
        validateCsvDataRows(dataLines, expectedAreaCodes, defaultHeaders);
    }
    
    /**
     * データ行形式検証（汎用版）
     * 各フィールドの基本的な形式を検証
     *
     * @param fields データ行のフィールド配列
     * @param rowNumber 行番号
     * @param expectedHeaders 期待されるヘッダーリスト
     */
    private static void validateDataRowFormat(String[] fields, int rowNumber, List<String> expectedHeaders) {
        // エリアCDの形式検証（最初のフィールドがエリアCDの場合）
        if (expectedHeaders.size() > 0 && expectedHeaders.get(0).contains("エリアCD")) {
            String areaCode = fields[0].trim().replace("\"", "");
            if (!areaCode.isEmpty()) {
                assertThat(areaCode)
                    .as("行%d エリアCD形式検証", rowNumber)
                    .matches("\\d{4}");
            }
        }

        // 企業CDの形式検証（ヘッダーに企業CDが含まれる場合）
        for (int i = 0; i < expectedHeaders.size() && i < fields.length; i++) {
            if (expectedHeaders.get(i).contains("企業CD")) {
                String kigyoCode = fields[i].trim().replace("\"", "");
                // 企業CDが空白または空文字列の場合は検証をスキップ（データ品質問題を許容）
                if (!kigyoCode.isEmpty() && !kigyoCode.matches("\\s*")) {
                    assertThat(kigyoCode)
                        .as("行%d 企業CD形式検証", rowNumber)
                        .matches("[A-Z0-9]+");
                }
                break;
            }
        }

        // 数値フィールドの検証（参考値等）
        for (int i = 0; i < expectedHeaders.size() && i < fields.length; i++) {
            String header = expectedHeaders.get(i);
            if (header.contains("累計") || header.contains("実績") || header.contains("計画")) {
                validateNumericField(fields[i], header, rowNumber);
            }
        }
    }

    /**
     * 後方互換性のためのメソッド
     * 次年度計画マスタ用のデフォルトヘッダーを使用
     */
    private static void validateDataRowFormat(String[] fields, int rowNumber) {
        List<String> defaultHeaders = createPlanMasterConfig(Arrays.asList()).getExpectedHeaders();
        validateDataRowFormat(fields, rowNumber, defaultHeaders);
    }
    
    /**
     * 数値フィールド検証
     * 数値フィールドが適切な形式であることを確認
     */
    private static void validateNumericField(String value, String fieldName, int rowNumber) {
        String cleanValue = value.trim().replace("\"", "");
        if (!cleanValue.isEmpty() && !cleanValue.equals("0")) {
            try {
                Long.parseLong(cleanValue);
            } catch (NumberFormatException e) {
                fail("行%d %s の数値形式が不正: %s", rowNumber, fieldName, cleanValue);
            }
        }
    }

    /**
     * CSV全体データ比較
     * 実際のCSVデータと期待されるデータを全体的に比較
     *
     * @param csvContent 実際のCSV内容
     * @param config CSV検証設定（期待データを含む）
     */
    private static void compareCsvData(String csvContent, CsvValidationConfig config) {
        logger.debug("CSV全体データ比較開始: 業務={}", config.getFileNamePrefix());

        String[] csvLines = csvContent.split("\n");
        List<String> expectedHeaders = config.getExpectedHeaders();
        List<List<String>> expectedDataRows = config.getExpectedDataRows();

        // Header行検証
        validateCsvHeader(csvLines[0], expectedHeaders);

        // データ行数の検証
        int actualDataRowCount = csvLines.length - 1; // ヘッダー行を除く
        int expectedDataRowCount = expectedDataRows.size();

        assertThat(actualDataRowCount)
            .as("データ行数が期待値と一致すること")
            .isEqualTo(expectedDataRowCount);

        // 各データ行の詳細比較
        for (int i = 0; i < expectedDataRowCount; i++) {
            String actualDataLine = csvLines[i + 1].trim(); // +1はヘッダー行をスキップ
            List<String> expectedDataRow = expectedDataRows.get(i);

            compareDataRow(actualDataLine, expectedDataRow, expectedHeaders, i + 1);
        }

        logger.debug("CSV全体データ比較完了: 業務={}, 比較行数={}", config.getFileNamePrefix(), expectedDataRowCount);
    }

    /**
     * CSV選択フィールド比較
     * 指定されたフィールドのみを対象としてCSVデータを比較
     *
     * @param csvContent 実際のCSV内容
     * @param config CSV検証設定（期待データと比較フィールドを含む）
     */
    private static void compareSelectedCsvFields(String csvContent, CsvValidationConfig config) {
        logger.debug("CSV選択フィールド比較開始: 業務={}", config.getFileNamePrefix());

        String[] csvLines = csvContent.split("\n");
        List<String> expectedHeaders = config.getExpectedHeaders();
        List<List<String>> expectedDataRows = config.getExpectedDataRows();
        List<String> compareFields = config.getCompareFields();

        // Header行検証
        validateCsvHeader(csvLines[0], expectedHeaders);

        // 比較対象フィールドのインデックスを取得
        List<Integer> compareFieldIndexes = getFieldIndexes(expectedHeaders, compareFields);

        // データ行数の検証
        int actualDataRowCount = csvLines.length - 1;
        int expectedDataRowCount = expectedDataRows.size();

        assertThat(actualDataRowCount)
            .as("データ行数が期待値と一致すること")
            .isEqualTo(expectedDataRowCount);

        // 各データ行の選択フィールド比較
        for (int i = 0; i < expectedDataRowCount; i++) {
            String actualDataLine = csvLines[i + 1].trim();
            List<String> expectedDataRow = expectedDataRows.get(i);

            compareSelectedDataRowFields(actualDataLine, expectedDataRow, compareFieldIndexes, compareFields, i + 1);
        }

        logger.debug("CSV選択フィールド比較完了: 業務={}, 比較行数={}, 比較フィールド数={}",
                    config.getFileNamePrefix(), expectedDataRowCount, compareFields.size());
    }

    /**
     * データ行全体比較
     * 実際のデータ行と期待されるデータ行を全フィールドで比較
     *
     * @param actualDataLine 実際のデータ行
     * @param expectedDataRow 期待されるデータ行
     * @param headers ヘッダーリスト
     * @param rowNumber 行番号
     */
    private static void compareDataRow(String actualDataLine, List<String> expectedDataRow,
                                     List<String> headers, int rowNumber) {
        String[] actualFields = actualDataLine.split(",");

        // フィールド数の検証
        assertThat(actualFields)
            .as("行%d のフィールド数が期待値と一致すること", rowNumber)
            .hasSize(expectedDataRow.size());

        // 各フィールドの値比較
        for (int i = 0; i < expectedDataRow.size(); i++) {
            String actualValue = cleanFieldValue(actualFields[i]);
            String expectedValue = expectedDataRow.get(i);
            String fieldName = i < headers.size() ? headers.get(i) : "フィールド" + (i + 1);

            assertThat(actualValue)
                .as("行%d %s の値が期待値と一致すること", rowNumber, fieldName)
                .isEqualTo(expectedValue);
        }

        logger.debug("データ行比較完了: 行={}, フィールド数={}", rowNumber, expectedDataRow.size());
    }

    /**
     * 選択フィールドデータ行比較
     * 実際のデータ行と期待されるデータ行を指定フィールドのみで比較
     *
     * @param actualDataLine 実際のデータ行
     * @param expectedDataRow 期待されるデータ行
     * @param compareFieldIndexes 比較対象フィールドのインデックスリスト
     * @param compareFields 比較対象フィールド名リスト
     * @param rowNumber 行番号
     */
    private static void compareSelectedDataRowFields(String actualDataLine, List<String> expectedDataRow,
                                                   List<Integer> compareFieldIndexes, List<String> compareFields, int rowNumber) {
        String[] actualFields = actualDataLine.split(",");

        // 選択フィールドのみを比較
        for (int i = 0; i < compareFieldIndexes.size(); i++) {
            int fieldIndex = compareFieldIndexes.get(i);
            String fieldName = compareFields.get(i);

            // フィールドインデックスの範囲チェック
            if (fieldIndex >= actualFields.length || fieldIndex >= expectedDataRow.size()) {
                fail("行%d フィールド%s のインデックス%dが範囲外です", rowNumber, fieldName, fieldIndex);
                continue;
            }

            String actualValue = cleanFieldValue(actualFields[fieldIndex]);
            String expectedValue = expectedDataRow.get(fieldIndex);

            assertThat(actualValue)
                .as("行%d %s の値が期待値と一致すること", rowNumber, fieldName)
                .isEqualTo(expectedValue);
        }

        logger.debug("選択フィールドデータ行比較完了: 行={}, 比較フィールド数={}", rowNumber, compareFields.size());
    }

    /**
     * フィールドインデックス取得
     * ヘッダーリストから指定フィールド名のインデックスを取得
     *
     * @param headers ヘッダーリスト
     * @param fieldNames 対象フィールド名リスト
     * @return フィールドインデックスリスト
     */
    private static List<Integer> getFieldIndexes(List<String> headers, List<String> fieldNames) {
        List<Integer> indexes = new ArrayList<>();

        for (String fieldName : fieldNames) {
            int index = headers.indexOf(fieldName);
            if (index == -1) {
                throw new IllegalArgumentException("指定されたフィールドが見つかりません: " + fieldName);
            }
            indexes.add(index);
        }

        return indexes;
    }

    /**
     * フィールド値のクリーニング
     * CSVフィールド値から引用符や余分な空白を除去
     *
     * @param fieldValue 元のフィールド値
     * @return クリーニング後のフィールド値
     */
    private static String cleanFieldValue(String fieldValue) {
        return fieldValue.trim().replace("\"", "");
    }

    /**
     * エクセルから指定シートの内容を読み込み
     *
     * @param filePath ファイルパース
     * @param sheetName シート名
     * @return シートの内容
     */
    public static List<List<String>> readExcelFileWithPadding(String filePath, String sheetName) throws IOException {
        List<List<String>> data = new ArrayList<>();
        try (InputStream inputStream = CsvContentComparator.class.getClassLoader().getResourceAsStream("testdata/" + filePath)) {
            Workbook workbook = new XSSFWorkbook(inputStream);

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet.getPhysicalNumberOfRows() == 0) {
                return data;
            }

            //headにより、カラム数を取得
            int columnCount = sheet.getRow(0).getLastCellNum();
            DataFormatter dataFormatter = new DataFormatter();

            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();

                for (int i = 0; i < columnCount; i++) {
                    Cell cell = row.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                    rowData.add(cell == null ? "" : dataFormatter.formatCellValue(cell));
                }
                data.add(rowData);
                System.out.println(rowData);
            }
        }catch (Exception e) {
            logger.error("検証用Excel テストデータ読み込みエラー: {}", e.getMessage(), e);
            throw new RuntimeException("検証用Excel テストデータ読み込みに失敗しました",e);
        }

        return data;
    }

    /**
     * 実際 CSVデータ取得
     * 期待されるCSVデータと実際のCSVデータを全体的に比較
     *
     * @param mockS3Service MockされたS3Service
     * @param zipFilePattern ZIPファイルのパターン設定
     */
    public static Map<String, String> getOutputCsvData(S3Service mockS3Service, String zipFilePattern) {
        Path zipFilePath = findGeneratedZipFile(zipFilePattern);
        logger.info("=== 実際 CSVデータ取得開始：パス={} ===", zipFilePath);

        try {
            verifyS3ServiceCalled(mockS3Service);
            Map<String, String> csvContents = extractCsvFilesFromZipFile(zipFilePath);

            logger.info("=== 実際 CSVデータ取得完了：パス={} ===", zipFilePath);
            return csvContents;
        } catch (Exception e) {
            logger.error("実際 CSVデータ取得エラー: {}", e.getMessage(), e);
            throw new RuntimeException("実際 CSVデータ取得に失敗しました", e);
        }
    }

    /**
     * 指定ディレクトリ配下のすべての CSV ファイルを処理し、Map に格納します
     * @param directoryPath ディレクトリパス
     * @return すべての CSV ファイル名と内容を含む Map
     * @throws IOException ディレクトリの走査またはファイルの読み取り時に発生する例外
     */
    public static Map<String, String> getExpectCsvData(String directoryPath) throws IOException {
        Map<String, String> resultMap = new LinkedHashMap<>();
        String projectRoot = System.getProperty("user.dir");

        Path directory = Paths.get(projectRoot, directoryPath);
        if (!directory.toFile().exists()) {
            throw new IllegalArgumentException("ファイルはありません：" + directory);
        }

        // ディレクトリが存在するかを確認する
        if (!Files.isDirectory(directory)) {
            throw new IllegalArgumentException("パスは有効なディレクトリではありません: " + directoryPath);
        }

        // ディレクトリ配下のすべてのファイルを走査し、CSV ファイルをフィルタリングする
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory, "*.csv")) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    Path path = Paths.get(file.toString());
                    String fileName = path.getFileName().toString();
                    List<String> lines = Files.readAllLines(path, StandardCharsets.UTF_8);
                    String content = String.join("\n", lines);
                    resultMap.put(fileName, content);
                }
            }
        }
        return resultMap;
    }

    /**
     * データ行全体比較
     * 実際のデータ行と期待されるデータ行を全フィールドで比較
     *
     * @param output 実際のデータ
     * @param expect 期待されるデータ
     */
    public static void compareCsvData(Map<String, String> output, Map<String, String> expect) {

        // 1.　データサイズとファイル名の比較
        assertThat(output.size())
                .as("実際のファイル数が期待値と一致すること")
                .isEqualTo(expect.size());

        Set<String> outputKeys = output.keySet();
        Set<String> expectKeys = expect.keySet();
        // expect にのみ存在するファイルを検索する
        Set<String> onlyInExpect = expectKeys.stream()
                .filter(key -> !outputKeys.contains(key))
                .collect(Collectors.toSet());

        // output にのみ存在するファイルを検索する
        Set<String> onlyInOutput = outputKeys.stream()
                .filter(key -> !expectKeys.contains(key))
                .collect(Collectors.toSet());
        // エラーメッセージを出力する
        if(!onlyInExpect.isEmpty()){
            fail(String.format(
                    "期待ファイル　%s は実際の出力ファイルに存在しません:\n" +
                            "期待ファイル: %s\n",
                    onlyInExpect, onlyInExpect));
        }
        if(!onlyInOutput.isEmpty()){
            fail(String.format(
                    "実際ファイル %s は期待ファイルに存在しません:\n" +
                            "実際ファイル: %s\n",
                    onlyInOutput, onlyInOutput));
        }

        // 2. データ比較
        for (Map.Entry<String, String> entry : output.entrySet()) {
            String fileName = entry.getKey();
            String outputContent = entry.getValue();

            String expectContent = expect.get(fileName);

            // データ分割
            List<String> outputLines = Arrays.asList(outputContent.split("\n"));
            List<String> expectLines = Arrays.asList(expectContent.split("\n"));

            assertThat(outputLines.size())
                    .as("ファイル「　%s　」のデータ数と期待値は異なる", fileName)
                    .isEqualTo(expectLines.size());

            // データ内容の比較
            for (int i = 0; i < expectLines.size(); i++) {
                String expectLine = expectLines.get(i);
                String outputLine = outputLines.get(i);

                // ヘーダの判断
                String lineType = (i == 0) ? "header" : "data[" + i + "]";

                if (!expectLine.equals(outputLine)) {
                    fail(String.format(
                            "ファイル「 %s 」の［　%s　］は不正:\n" +
                                    "期待値: %s\n" +
                                    "実際値: %s",
                            fileName, lineType, expectLine, outputLine
                    ));
                }
            }
        }

    }
}
