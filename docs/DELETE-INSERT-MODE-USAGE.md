# 削除-挿入モード使用ガイド

## 概要

ImportTemplateに新しく追加された削除-挿入モードは、データインポート時に以下の処理を行います：

1. **削除フェーズ**: 各データ行に基づいて動的に生成された条件で既存データを削除
2. **挿入フェーズ**: 新しいデータをバッチ挿入

## 主な特徴

- **データ駆動型削除**: 各データ行の内容に基づいて異なる削除条件を適用可能
- **バッチ最適化**: 同じ削除条件のレコードをグループ化して効率的に処理
- **トランザクション安全**: 削除と挿入が同一トランザクション内で実行
- **既存コード互換**: 既存のUPSERTモードに影響なし

## 基本的な使用方法

### 1. ImportOptionsでの設定

```java
@Override
protected ImportOptions buildImportOptions() {
    return ImportOptions.builder()
            .format(FileFormat.CSV)
            .hasHeader(true)
            .targetTable("T_PLAN_DATA")
            // 削除-挿入モードを有効化
            .deleteInsertMode(this::generateDeleteCondition)
            .build();
}
```

### 2. 削除条件生成メソッドの実装

```java
/**
 * 削除条件生成ロジック
 */
private DeleteCondition generateDeleteCondition(YourDTO dto, ImportOptions options) {
    List<String> whereColumns = new ArrayList<>();
    List<Object> whereValues = new ArrayList<>();

    // 基本的な削除条件
    whereColumns.add("YEAR");
    whereValues.add(dto.getYear());
    
    whereColumns.add("AREA_CODE");
    whereValues.add(dto.getAreaCode());

    // 条件付き削除条件
    if (dto.getCategory() != null) {
        whereColumns.add("CATEGORY");
        whereValues.add(dto.getCategory());
    }

    return new DeleteCondition(whereColumns, whereValues);
}
```

## 使用シナリオ例

### シナリオ1: 年度・エリア別データの完全置換

```java
private DeleteCondition generateDeleteCondition(PlanData dto, ImportOptions options) {
    return new DeleteCondition(
        Arrays.asList("NENDO", "AREA_CODE"),
        Arrays.asList(dto.getNendo(), dto.getAreaCode())
    );
}
```

**実行結果**: 同じ年度・エリアの既存データがすべて削除され、新しいデータが挿入される

### シナリオ2: データタイプ別の動的削除条件

```java
private DeleteCondition generateDeleteCondition(PlanData dto, ImportOptions options) {
    List<String> columns = new ArrayList<>();
    List<Object> values = new ArrayList<>();
    
    // 共通条件
    columns.add("NENDO");
    values.add(dto.getNendo());
    
    // データタイプ別条件
    if ("MONTHLY".equals(dto.getDataType())) {
        columns.add("MONTH");
        values.add(dto.getMonth());
    } else if ("QUARTERLY".equals(dto.getDataType())) {
        columns.add("QUARTER");
        values.add(dto.getQuarter());
    }
    
    return new DeleteCondition(columns, values);
}
```

### シナリオ3: 条件付き削除（一部データのみ削除）

```java
private DeleteCondition generateDeleteCondition(ProductData dto, ImportOptions options) {
    // 特定条件の場合のみ削除を実行
    if ("REPLACE".equals(dto.getImportMode())) {
        return new DeleteCondition(
            Arrays.asList("PRODUCT_ID", "VERSION"),
            Arrays.asList(dto.getProductId(), dto.getVersion())
        );
    }
    
    // nullを返すと削除をスキップ
    return null;
}
```

## 設定オプション

### バッチ最適化の制御

```java
// バッチ最適化有効（デフォルト）
.deleteInsertMode(this::generateDeleteCondition)

// バッチ最適化無効（シンプルモード）
.deleteInsertModeSimple(this::generateDeleteCondition)
```

### バッチ最適化の動作

- **有効時**: 同じSQL文の削除操作をグループ化してバッチ実行
- **無効時**: 各レコードに対して個別に削除→挿入を実行

## パフォーマンス考慮事項

### 推奨事項

1. **バッチサイズの調整**: 大量データの場合は適切なバッチサイズを設定
2. **インデックスの確認**: 削除条件の列にインデックスが設定されていることを確認
3. **バッチ最適化の活用**: 同じ削除条件が多い場合はバッチ最適化を有効にする

### 注意事項

1. **外部キー制約**: 削除対象テーブルに外部キー制約がある場合は注意
2. **トランザクションサイズ**: 大量削除の場合はトランザクションサイズを考慮
3. **ロック競合**: 同時実行時のロック競合に注意

## エラーハンドリング

### 一般的なエラーと対処法

1. **DeleteConditionProviderが未設定**
   ```
   エラー: DELETE_INSERT モードでは DeleteConditionProvider の設定が必要です
   対処: ImportOptionsでdeleteInsertMode()を呼び出す
   ```

2. **削除条件の列数と値数の不一致**
   ```
   エラー: WHERE列数と値の数が一致しません
   対処: DeleteConditionのコンストラクタで列名と値の数を一致させる
   ```

## 既存コードからの移行

### UPSERTモードから削除-挿入モードへの変更

```java
// 変更前（UPSERTモード）
return ImportOptions.builder()
        .targetTable("T_DATA")
        .keyColumns("ID", "VERSION")
        .upsertMode(true)
        .build();

// 変更後（削除-挿入モード）
return ImportOptions.builder()
        .targetTable("T_DATA")
        .deleteInsertMode(this::generateDeleteCondition)
        .build();
```

### 段階的移行のアプローチ

1. **テスト環境での検証**: まずテスト環境で新しいモードを検証
2. **パフォーマンステスト**: 大量データでのパフォーマンスを確認
3. **本番環境への適用**: 段階的に本番環境に適用

## まとめ

削除-挿入モードは、従来のUPSERTモードでは対応できない複雑な削除条件を持つデータ更新シナリオに対応します。適切に使用することで、データの完全置換や条件付き更新を効率的に実行できます。
