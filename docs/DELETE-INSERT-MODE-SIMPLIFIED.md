# 削除-挿入モード簡化実装完了

## 実装概要

ImportTemplateに削除-挿入モードを追加し、要求に応じて実装を簡化しました。

## 簡化された実装

### 1. 削除された複雑な機能
- ❌ `executeSimpleDeleteInsert`メソッド（削除）
- ❌ `DeleteExecutionPlan`クラス（削除）
- ❌ `batchDeleteOptimization`設定オプション（削除）
- ❌ `deleteInsertModeSimple`ビルダーメソッド（削除）
- ❌ Spring関連アノテーション（削除）

### 2. 簡化されたアーキテクチャ

#### 核心クラス
```java
// DeleteConditionProvider.java - シンプルな関数型インターフェース
@FunctionalInterface
public interface DeleteConditionProvider<T extends DatabaseMappable> {
    DeleteCondition generateDeleteCondition(T dto, String tableName);
}

// DeleteCondition.java - 削除条件とSQLを内部管理
public class DeleteCondition {
    private final String deleteSql;
    private final List<Object> parameters;
    
    public DeleteCondition(List<String> whereColumns, List<Object> whereValues, String tableName) {
        // 内部でSQLを構築
    }
}
```

#### 設定方法
```java
@Override
protected ImportOptions buildImportOptions() {
    return ImportOptions.builder()
            .targetTable("T_PLAN_DATA")
            .deleteInsertMode(this::generateDeleteCondition)  // シンプル設定
            .build();
}

private DeleteCondition generateDeleteCondition(YourDTO dto, String tableName) {
    return new DeleteCondition(
        Arrays.asList("YEAR", "AREA_CODE"),
        Arrays.asList(dto.getYear(), dto.getAreaCode()),
        tableName
    );
}
```

## 処理フロー

1. **データ解析**: CSVファイルをDTOにマッピング
2. **削除条件生成**: 各DTOに対してDeleteConditionを生成
3. **自動グループ化**: 同じ削除SQLを持つレコードを自動グループ化
4. **バッチ削除**: グループ化されたレコードをバッチで削除
5. **バッチ挿入**: 新しいデータをバッチで挿入

## 主な特徴

### ✅ 保持された機能
- **データ駆動削除**: 各データ行に基づく動的削除条件
- **自動バッチ最適化**: 同じSQLのレコードを自動グループ化
- **トランザクション安全**: 削除と挿入が同一トランザクション内
- **既存互換**: UPSERTモードに影響なし

### ✅ 簡化された点
- **設定不要**: バッチ最適化は常に有効
- **シンプルAPI**: 最小限のメソッドとクラス
- **直感的使用**: 複雑な設定オプションなし

## 実装されたファイル

### 修正されたファイル
1. `ImportResult.java` - deletedCountフィールド追加
2. `ImportOptions.java` - deleteConditionProvider追加、簡化
3. `ImportTemplate.java` - 削除-挿入処理追加、簡化
4. `AbstractImportService.java` - ログ出力更新

### 新規作成されたファイル
1. `DeleteConditionProvider.java` - 削除条件生成インターフェース
2. `DeleteCondition.java` - 削除条件クラス（簡化版）
3. `PlanDataDeleteInsertImportService.java` - 使用例（簡化版）

### 削除されたファイル
1. `DeleteExecutionPlan.java` - 不要になったため削除

## 使用例

```java
public class SimpleDeleteInsertImportService extends AbstractImportService<YourDTO> {
    
    @Override
    protected ImportOptions buildImportOptions() {
        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .targetTable("T_YOUR_TABLE")
                .deleteInsertMode(this::generateDeleteCondition)
                .build();
    }
    
    private DeleteCondition generateDeleteCondition(YourDTO dto, String tableName) {
        // 年度とエリアで既存データを削除
        return new DeleteCondition(
            Arrays.asList("YEAR", "AREA_CODE"),
            Arrays.asList(dto.getYear(), dto.getAreaCode()),
            tableName
        );
    }
    
    // その他の必要なメソッド実装...
}
```

## まとめ

要求に応じて削除-挿入モードを大幅に簡化しました：

- **複雑なモード削除**: シンプルな削除-挿入のみ保持
- **アーキテクチャ簡化**: 必要最小限のクラス構成
- **Spring依存削除**: フレームワーク非依存の実装
- **使用例簡化**: 基本機能のみのデモンストレーション

この簡化により、コードの可読性と保守性が向上し、コンパイルエラーのリスクも最小化されました。核心機能は保持しつつ、使いやすさを重視した実装となっています。
