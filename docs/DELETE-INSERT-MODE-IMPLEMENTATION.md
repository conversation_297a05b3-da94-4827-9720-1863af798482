# 削除-挿入モード実装完了報告（簡化版）

## 実装概要

ImportTemplateに削除-挿入モードを追加しました。このモードでは、各データ行に基づいて動的に削除条件を生成し、既存データを削除してから新しいデータを挿入します。

## 実装したファイル

### 1. 核心インターフェースとクラス

#### `DeleteConditionProvider.java`
- 削除条件を動的に生成するための関数型インターフェース
- シンプルな設計：`DeleteCondition generateDeleteCondition(T dto, String tableName)`

#### `DeleteCondition.java`
- 削除条件を表現するクラス
- 削除SQLとパラメータを内部で管理
- バッチ最適化のための自動グループ化をサポート

### 2. 既存クラスの拡張

#### `ImportResult.java`
- `deletedCount`フィールドを追加
- 削除件数の記録に対応

#### `ImportOptions.java`
- `deleteConditionProvider`フィールドを追加
- 削除-挿入モード判定メソッド`isDeleteInsertMode()`を追加
- ビルダーに便利メソッド`deleteInsertMode()`を追加

#### `ImportTemplate.java`
- `processBatch`メソッドを処理モード対応に重構
- 以下の新しいメソッドを追加：
  - `processUpsertMode()`: 既存のUPSERTロジック
  - `processInsertOnlyMode()`: 挿入のみモード
  - `processDeleteInsertMode()`: 削除-挿入モード
  - `executeBatchOptimizedDeleteInsert()`: バッチ最適化削除-挿入

#### `AbstractImportService.java`
- ログ出力を削除件数に対応

## 主な機能特徴

### 1. データ駆動型削除
- 各データ行の内容に基づいて異なる削除条件を適用可能
- 条件付き削除（nullを返すことで削除をスキップ）をサポート

### 2. 自動バッチ最適化
- 同じ削除SQLのレコードを自動的にグループ化
- バッチ削除でパフォーマンスを向上
- 設定不要で自動的に最適化

### 3. トランザクション安全
- 削除と挿入が同一トランザクション内で実行
- エラー時の自動ロールバック

### 4. 既存コード互換
- 既存のUPSERTモードに影響なし
- デフォルト動作は変更なし

## 使用方法

### 基本的な設定

```java
@Override
protected ImportOptions buildImportOptions() {
    return ImportOptions.builder()
            .format(FileFormat.CSV)
            .hasHeader(true)
            .targetTable("T_PLAN_DATA")
            // 削除-挿入モードを有効化
            .deleteInsertMode(this::generateDeleteCondition)
            .build();
}

private DeleteCondition generateDeleteCondition(YourDTO dto, String tableName) {
    List<String> whereColumns = Arrays.asList("YEAR", "AREA_CODE");
    List<Object> whereValues = Arrays.asList(dto.getYear(), dto.getAreaCode());
    return new DeleteCondition(whereColumns, whereValues, tableName);
}
```

### 高度な使用例

```java
// シンプルな削除条件
private DeleteCondition generateDeleteCondition(PlanData dto, String tableName) {
    List<String> columns = Arrays.asList("NENDO", "AREA_CODE");
    List<Object> values = Arrays.asList(dto.getNendo(), dto.getAreaCode());

    return new DeleteCondition(columns, values, tableName);
}
```

## ドキュメントと例

### ドキュメント
- `DELETE-INSERT-MODE-USAGE.md`: 使用ガイド
- `PlanDataDeleteInsertImportService.java`: 簡化された実装例

## パフォーマンス考慮事項

### 推奨設定
1. **バッチサイズ**: 1000-5000件（データサイズによる）
2. **インデックス**: 削除条件の列にインデックスを設定
3. **削除条件**: 効率的な削除条件を設計

### 注意事項
1. **外部キー制約**: 削除対象テーブルの制約を確認
2. **大量削除**: トランザクションサイズとロック時間を考慮
3. **同時実行**: ロック競合の可能性

## まとめ

削除-挿入モードの簡化実装により、従来のUPSERTモードでは対応できないデータ更新シナリオに対応できるようになりました。

### 主な改善点
- **シンプル設計**: 複雑な設定オプションを排除
- **自動最適化**: バッチ処理の最適化を自動実行
- **直感的使用**: 最小限のコードで削除-挿入処理を実現
- **既存互換**: 既存のUPSERTモードに影響なし

この実装により、データの完全置換や条件付き更新を効率的かつ簡単に実行できます。
