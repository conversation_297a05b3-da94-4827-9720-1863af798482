# 削除-挿入モード実装完了報告

## 実装概要

ImportTemplateに削除-挿入モードを追加しました。このモードでは、各データ行に基づいて動的に削除条件を生成し、既存データを削除してから新しいデータを挿入します。

## 実装したファイル

### 1. 核心インターフェースとクラス

#### `DeleteConditionProvider.java`
- 削除条件を動的に生成するための関数型インターフェース
- 各データ行（DTO）に基づいて`DeleteCondition`を生成

#### `DeleteCondition.java`
- 削除条件（WHERE句の列名と値）を表現するクラス
- 削除SQLの構築とバッチ最適化キーの生成機能

#### `DeleteExecutionPlan.java`
- バッチ削除最適化用の実行計画クラス
- 同じSQL文で実行される削除操作のパラメータを管理

### 2. 既存クラスの拡張

#### `ImportResult.java`
- `deletedCount`フィールドを追加
- 削除件数の記録に対応

#### `ImportOptions.java`
- `deleteConditionProvider`フィールドを追加
- `batchDeleteOptimization`フィールドを追加
- 削除-挿入モード判定メソッド`isDeleteInsertMode()`を追加
- ビルダーに便利メソッド`deleteInsertMode()`と`deleteInsertModeSimple()`を追加

#### `ImportTemplate.java`
- `processBatch`メソッドを処理モード対応に重構
- 以下の新しいメソッドを追加：
  - `processUpsertMode()`: 既存のUPSERTロジック
  - `processInsertOnlyMode()`: 挿入のみモード
  - `processDeleteInsertMode()`: 削除-挿入モード
  - `executeBatchOptimizedDeleteInsert()`: バッチ最適化削除-挿入
  - `executeSimpleDeleteInsert()`: シンプル削除-挿入

#### `AbstractImportService.java`
- ログ出力を削除件数に対応

## 主な機能特徴

### 1. データ駆動型削除
- 各データ行の内容に基づいて異なる削除条件を適用可能
- 条件付き削除（nullを返すことで削除をスキップ）をサポート

### 2. バッチ最適化
- 同じ削除条件（列の組み合わせ）のレコードをグループ化
- バッチ削除でパフォーマンスを向上
- 最適化の有効/無効を選択可能

### 3. トランザクション安全
- 削除と挿入が同一トランザクション内で実行
- エラー時の自動ロールバック

### 4. 既存コード互換
- 既存のUPSERTモードに影響なし
- デフォルト動作は変更なし

## 使用方法

### 基本的な設定

```java
@Override
protected ImportOptions buildImportOptions() {
    return ImportOptions.builder()
            .format(FileFormat.CSV)
            .hasHeader(true)
            .targetTable("T_PLAN_DATA")
            // 削除-挿入モードを有効化
            .deleteInsertMode(this::generateDeleteCondition)
            .build();
}

private DeleteCondition generateDeleteCondition(YourDTO dto, ImportOptions options) {
    List<String> whereColumns = Arrays.asList("YEAR", "AREA_CODE");
    List<Object> whereValues = Arrays.asList(dto.getYear(), dto.getAreaCode());
    return new DeleteCondition(whereColumns, whereValues);
}
```

### 高度な使用例

```java
// データタイプ別の動的削除条件
private DeleteCondition generateDeleteCondition(PlanData dto, ImportOptions options) {
    List<String> columns = new ArrayList<>();
    List<Object> values = new ArrayList<>();
    
    // 基本条件
    columns.add("NENDO");
    values.add(dto.getNendo());
    
    // データタイプ別条件
    if ("MONTHLY".equals(dto.getDataType())) {
        columns.add("MONTH_CODE");
        values.add(dto.getMonthCode());
    } else if ("QUARTERLY".equals(dto.getDataType())) {
        columns.add("QUARTER_CODE");
        values.add(dto.getQuarterCode());
    }
    
    return new DeleteCondition(columns, values);
}
```

## テストとドキュメント

### テストファイル
- `DeleteConditionTest.java`: 単体テスト
- `DeleteInsertModeIntegrationTest.java`: 統合テスト

### ドキュメント
- `DELETE-INSERT-MODE-USAGE.md`: 使用ガイド
- `PlanDataDeleteInsertImportService.java`: 実装例

## パフォーマンス考慮事項

### 推奨設定
1. **バッチサイズ**: 1000-5000件（データサイズによる）
2. **バッチ最適化**: 同じ削除条件が多い場合は有効
3. **インデックス**: 削除条件の列にインデックスを設定

### 注意事項
1. **外部キー制約**: 削除対象テーブルの制約を確認
2. **大量削除**: トランザクションサイズとロック時間を考慮
3. **同時実行**: ロック競合の可能性

## 今後の拡張可能性

1. **カスタム削除SQL**: より複雑な削除条件のサポート
2. **削除前バックアップ**: 削除データの自動バックアップ
3. **削除統計**: より詳細な削除統計情報
4. **非同期削除**: 大量データの非同期削除処理

## まとめ

削除-挿入モードの実装により、従来のUPSERTモードでは対応できない複雑なデータ更新シナリオに対応できるようになりました。実装は既存コードとの互換性を保ちながら、柔軟で効率的な削除-挿入処理を提供します。
